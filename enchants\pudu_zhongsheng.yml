display-name: "普度众生"
description: "自身获取的经验值增加&a%exp_boost%%&r。自身获取经验值时，同公会的人在&a%radius%&r格内也会增加同样数值的经验值"
placeholders:
  exp_boost: "10 + (%level% - 1) * 20"
  radius: "3 + (%level% - 1)"
type: normal

targets:
  - chestplate
conflicts: []
rarity: epic
max-level: 10

tradeable: false
discoverable: false
enchantable: false

effects:
  - id: xp_multiplier
    args:
      multiplier: "1.1 + (%level% - 1) * 0.2"

  - id: aoe
    args:
      radius: "3 + (%level% - 1)"
      shape: circle
      effects:
        - id: give_xp
          args:
            amount: "%trigger_value% * (0.1 + (%level% - 1) * 0.2)"
    triggers:
      - gain_xp

conditions: []
