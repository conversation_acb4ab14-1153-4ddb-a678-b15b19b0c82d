# EcoEnchants 自定义附魔系统总结

## 概述
根据用户需求，我们已经完全重新设计了EcoEnchants插件的附魔系统，包含6个类别的附魔，总计89个自定义附魔。

## 附魔分类

### 1. 限定类附魔 (5个)
- **万象气息** - 极大增加幸运度，适用于锹斧镐
- **混沌威压** - 攻击时有几率造成最大生命值25%伤害，适用于剑弓弩斧
- **一剑隔世** - 击杀玩家降低等级，低等级直接秒杀，适用于剑
- **绝望之力** - 击杀时有几率获得攻击力加成，适用于剑
- **睥睨苍生** - 攻击削弱护甲，低护甲直接秒杀，适用于剑

### 2. 传说类附魔 (5个)
- **精进** - 经验获取倍率提升，适用于多种工具
- **万象天引** - 自动拾取掉落物和经验，适用于防具
- **永葆之躯** - 低血量时自动回血，适用于防具
- **圣光领域** - 范围治疗效果，适用于头盔
- **普度众生** - 范围经验加成，适用于胸甲

### 3. 史诗类附魔 (30个)
包括绑定、无限耐久、吸血、再植、泰山、斗转星移、重伤、AOE、重甲克星、致盲、中毒、虚弱、减速、眩晕、凋零、钢铁之躯、新陈代谢、锻造、引力、击飞、退散、治愈之光、利刃、播种机、中国制造、破虚、矿石洗练、盾构机、炸弹箭矢、不服气来干我呀

### 4. 罕见类附魔 (42个)
包括吞噬、横扫千军、石化皮肤、牵引、刺骨、伤害加强、激素、防御加强、扎刺、速度加强、鳃、夜视、袋鼠、猎豹、岩浆行走、移动修补、矿元素亲和、挖掘机、破甲、天罚、快速装填、海族克星、忠诚、耐性、煽动、规劝、制动、植物亲和、各种播种机、青蛙跳、老六、夜伏、蓄力一击、矿元素亲和2、潜力爆发、矿脉联锁、连跳跳、超级能量、节能科技

### 5. 普通类附魔
保留原版普通附魔

### 6. 诅咒类附魔 (10个)
- **寄生虫** - 增加饥饿消耗
- **重负** - 减速和采矿疲劳
- **易碎** - 移动时损耗耐久
- **绑定诅咒** - 无法卸下装备
- **消失诅咒** - 死亡时物品消失
- **烙印诅咒** - 无法在铁砧修改
- **劣质品诅咒** - 额外耐久消耗
- **霉运诅咒** - 挖掘有几率失败
- **空虚诅咒** - 击杀无掉落和经验
- **经验修补诅咒** - 强制消耗所有经验修复

## 配置文件更新

### rarity.yml
- 添加了"限定"和"传说"稀有度等级
- 限定类和传说类附魔无法通过常规方式获得（table-chance: 0）

### types.yml
- 添加了"limited"和"legendary_type"类型
- 限定类附魔无法被砂轮洗去

### targets.yml
- 添加了"firework_rocket"和"all"目标类型
- 支持烟花火箭和所有物品的附魔

## 特殊机制

### 限定类附魔特点：
- 必须绑定用户
- 只能给有绑定的装备使用
- 无限等级
- 无法通过常规方式获得
- 无法交易

### 传说类附魔特点：
- 氪金获得
- 最高10级
- 无法通过常规方式获得
- 效果强大且独特

### 升级系统：
- 史诗类：1-10级通过抽奖宝石，10级以上需收费宝石，最大20级
- 罕见类：通过相同附魔宝石升级，最大等级各不相同
- 诅咒类：大多数不可升级

## 文件结构
```
enchants/
├── 限定类附魔 (5个文件)
├── 传说类附魔 (5个文件)  
├── 史诗类附魔 (30个文件)
├── 罕见类附魔 (42个文件)
└── 诅咒类附魔 (10个文件)
```

## 注意事项
1. 所有附魔文件已创建完成
2. 配置文件已更新以支持新的附魔系统
3. 建议在服务器上测试所有附魔效果
4. 部分复杂效果可能需要额外的插件支持
5. 限定类和传说类附魔的获取方式需要通过其他系统实现

## 下一步建议
1. 在测试服务器上加载插件测试
2. 根据实际效果调整数值平衡
3. 实现限定类和传说类附魔的获取系统
4. 添加附魔宝石升级系统
5. 测试所有附魔的兼容性和性能影响
