# EcoEnchants 附魔系统测试方案
## 适用于 Paper 1.18.2

### 测试前准备
1. **服务器环境**
   - 确保使用 Paper 1.18.2
   - 安装最新版本的 EcoEnchants 插件
   - 确保服务器有足够的权限进行测试

2. **测试环境设置**
   ```
   /gamemode creative <玩家>
   /tp <玩家> 0 100 0
   /weather clear
   /time set day
   ```

3. **插件重载**
   ```
   /ecoenchants reload
   ```

### 测试分类

## 一、限定类附魔测试

### 1. 万象气息测试
**目标**: 验证幸运度增加效果
```
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:万象气息",lvl:5}]} 1
```
**测试步骤**:
1. 装备附魔镐子
2. 查看玩家状态效果 (应该有幸运效果)
3. 挖掘矿石观察掉落物增加

### 2. 混沌威压测试
**目标**: 验证最大生命值25%伤害
```
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:混沌威压",lvl:5}]} 1
```
**测试步骤**:
1. 生成高血量怪物: `/summon zombie ~ ~ ~ {Health:100f,Attributes:[{Name:generic.max_health,Base:100}]}`
2. 用附魔剑攻击
3. 观察是否有几率造成25点伤害

### 3. 一剑隔世测试
**目标**: 验证等级降低和秒杀效果
```
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:一剑隔世",lvl:3}]} 1
```
**测试步骤**:
1. 创建测试玩家并设置等级: `/xp set <测试玩家> 10L`
2. 用附魔剑击杀测试玩家
3. 检查测试玩家等级是否降低

### 4. 绝望之力测试
**目标**: 验证击杀后力量效果
```
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:绝望之力",lvl:1}]} 1
```
**测试步骤**:
1. 生成怪物: `/summon zombie ~ ~ ~`
2. 击杀怪物
3. 检查是否获得力量效果

### 5. 睥睨苍生测试
**目标**: 验证护甲削弱和秒杀效果
```
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:睥睨苍生",lvl:1}]} 1
```
**测试步骤**:
1. 生成穿装备的僵尸
2. 攻击观察虚弱效果
3. 攻击无装备怪物观察秒杀效果

## 二、传说类附魔测试

### 1. 精进测试
**目标**: 验证经验倍率增加
```
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:精进",lvl:3}]} 1
```
**测试步骤**:
1. 记录当前经验值
2. 手持附魔武器击杀怪物
3. 对比经验获取量

### 2. 万象天引测试
**目标**: 验证自动拾取功能
```
/give <玩家> diamond_helmet{Enchantments:[{id:"ecoenchants:万象天引",lvl:2}]} 1
```
**测试步骤**:
1. 穿戴附魔头盔
2. 在附近丢弃物品
3. 观察物品是否自动拾取

### 3. 永葆之躯测试
**目标**: 验证低血量自动回血
```
/give <玩家> diamond_chestplate{Enchantments:[{id:"ecoenchants:永葆之躯",lvl:1}]} 1
```
**测试步骤**:
1. 穿戴附魔胸甲
2. 将血量降至2点以下: `/effect give <玩家> instant_damage 1 10`
3. 受到攻击观察是否自动回血

## 三、史诗类附魔测试

### 1. 绑定测试
**目标**: 验证死亡不掉落
```
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:绑定",lvl:1}]} 1
```
**测试步骤**:
1. 手持附魔物品
2. 死亡: `/kill <玩家>`
3. 重生后检查物品是否还在

### 2. 无限耐久测试
**目标**: 验证耐久不减少
```
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:无限耐久",lvl:1}],Damage:1000} 1
```
**测试步骤**:
1. 使用损坏的附魔工具
2. 大量使用工具
3. 观察耐久度变化

### 3. 锻造测试
**目标**: 验证自动冶炼
```
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:锻造",lvl:1}]} 1
```
**测试步骤**:
1. 挖掘铁矿石
2. 观察是否直接掉落铁锭

### 4. 破虚测试
**目标**: 验证飞行能力
```
/give <玩家> diamond_boots{Enchantments:[{id:"ecoenchants:破虚",lvl:1}]} 1
```
**测试步骤**:
1. 穿戴附魔靴子
2. 尝试飞行
3. 观察耐久度消耗

## 四、罕见类附魔测试

### 1. 挖掘机测试
**目标**: 验证范围挖掘
```
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:挖掘机",lvl:3}]} 1
```
**测试步骤**:
1. 建造石头墙
2. 挖掘一个方块
3. 观察是否范围破坏

### 2. 矿脉联锁测试
**目标**: 验证矿脉挖掘
```
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:矿脉联锁",lvl:1}]} 1
```
**测试步骤**:
1. 生成矿脉: `/fill ~-3 ~-3 ~-3 ~3 ~3 ~3 diamond_ore`
2. 挖掘一个矿石
3. 观察是否连锁挖掘

### 3. 连跳跳测试
**目标**: 验证二段跳
```
/give <玩家> diamond_boots{Enchantments:[{id:"ecoenchants:连跳跳",lvl:1}]} 1
```
**测试步骤**:
1. 穿戴附魔靴子
2. 跳跃到空中
3. 在空中按下潜行键测试二段跳

## 五、诅咒类附魔测试

### 1. 重负测试
**目标**: 验证负面效果
```
/give <玩家> diamond_helmet{Enchantments:[{id:"ecoenchants:重负",lvl:2}]} 1
```
**测试步骤**:
1. 穿戴附魔头盔
2. 观察移动速度和挖掘速度
3. 检查状态效果

### 2. 易碎测试
**目标**: 验证耐久损耗
```
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:易碎",lvl:1}]} 1
```
**测试步骤**:
1. 手持附魔物品
2. 等待一段时间
3. 观察耐久度变化

## 测试记录表

| 附魔名称 | 测试结果 | 问题描述 | 修复状态 |
|---------|---------|---------|---------|
| 万象气息 | ⭕ | | |
| 混沌威压 | ⭕ | | |
| 一剑隔世 | ⭕ | | |
| 绝望之力 | ⭕ | | |
| 睥睨苍生 | ⭕ | | |
| 精进 | ⭕ | | |
| 万象天引 | ⭕ | | |
| 永葆之躯 | ⭕ | | |
| 圣光领域 | ⭕ | | |
| 普度众生 | ⭕ | | |

### 测试结果说明
- ✅ 正常工作
- ❌ 存在问题
- ⭕ 待测试
- 🔧 需要修复

### 常见问题排查
1. **附魔不生效**: 检查插件是否正确加载，查看控制台错误
2. **效果不正确**: 检查配置文件语法，验证效果ID是否正确
3. **触发器问题**: 确认触发条件是否满足
4. **权限问题**: 检查玩家是否有相应权限

### 性能测试
1. 服务器TPS监控
2. 内存使用情况
3. 大量附魔同时使用的稳定性
