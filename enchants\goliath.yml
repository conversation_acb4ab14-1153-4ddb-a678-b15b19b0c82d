display-name: "劣势转化"
description: "对血量高于你的目标每倍血量额外造成 &a%damage%x &r伤害, 上限 &a%limit%x"
placeholders:
  damage: "0.2 + %level% * 0.1" #"0.5 + %level% * 0.1"
  limit: "0.8 + %level% * 0.4" #"1.6 + %level% * 0.4"
type: normal

targets:
  - sword
conflicts: [ ]
rarity: rare
max-level: 6

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: damage_multiplier
    args:
      multiplier: "1 + min(max(%victim_health% / %player_health% - 1, 0) * (0.2 + %level% * 0.1), 0.8 + %level% * 0.4)"
    triggers:
      - melee_attack

conditions: [ ]
