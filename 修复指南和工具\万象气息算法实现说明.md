# 万象气息算法实现说明
## 复杂迭代算法的EcoEnchants实现挑战

### 🎯 万象气息的正确算法

**原始需求**: 当前幸运度+（1-当前幸运度）×0.2，每升1级迭代计算1次

#### 算法详解
```
初始幸运度 = 0
每次迭代: 新幸运度 = 当前幸运度 + (1 - 当前幸运度) × 0.2

1级: 0 + (1-0) × 0.2 = 0.2 (20%幸运度)
2级: 0.2 + (1-0.2) × 0.2 = 0.36 (36%幸运度)
3级: 0.36 + (1-0.36) × 0.2 = 0.488 (48.8%幸运度)
4级: 0.488 + (1-0.488) × 0.2 = 0.5904 (59.04%幸运度)
5级: 0.5904 + (1-0.5904) × 0.2 = 0.67232 (67.232%幸运度)
...
10级: 约89.26%幸运度
20级: 约99.01%幸运度
```

### 🚨 EcoEnchants实现限制

#### 问题分析
1. **复杂数学运算**: EcoEnchants无法执行复杂的迭代算法
2. **状态记忆**: 无法记住"当前幸运度"状态
3. **动态计算**: 无法在运行时进行复杂的数学计算

#### 当前实现
```yaml
effects:
  - id: potion_effect
    args:
      effect: luck
      level: "%level%"
      duration: 999999
    triggers:
      - static_20
```

**问题**: 这只是简单的给予幸运等级=附魔等级，不是迭代算法

### 💡 可能的解决方案

#### 方案1: 近似映射表（推荐）
创建一个近似的等级映射，模拟迭代结果：

```yaml
# 根据迭代算法计算出的近似值
placeholders:
  luck_level: |
    %level% == 1 ? 1 :
    %level% == 2 ? 2 :
    %level% == 3 ? 2 :
    %level% == 4 ? 3 :
    %level% == 5 ? 3 :
    %level% == 10 ? 4 :
    %level% == 20 ? 5 :
    floor(%level% / 4)
```

#### 方案2: 数学近似公式
使用一个近似公式模拟迭代效果：

```yaml
placeholders:
  luck_level: "floor(%level% * 0.6 + 1)"
```

#### 方案3: 分段函数
为不同等级范围设置不同的幸运等级：

```yaml
placeholders:
  luck_level: |
    %level% <= 5 ? floor(%level% * 0.8) :
    %level% <= 15 ? floor(%level% * 0.6) :
    floor(%level% * 0.4)
```

#### 方案4: 多个附魔文件
为关键等级创建单独的附魔文件，每个文件有精确的幸运等级。

### 🔧 当前实现评估

#### 现状
- **实现**: 简单的幸运等级=附魔等级
- **优点**: 简单可靠，容易理解
- **缺点**: 不符合原始的迭代算法

#### 效果对比

| 附魔等级 | 理论迭代结果 | 当前实现 | 差异 |
|----------|-------------|----------|------|
| 1级 | 20%幸运度(约1级) | 幸运1级 | 符合 |
| 5级 | 67%幸运度(约3级) | 幸运5级 | 偏高 |
| 10级 | 89%幸运度(约4级) | 幸运10级 | 偏高 |
| 20级 | 99%幸运度(约5级) | 幸运20级 | 偏高 |

### 📊 推荐实现方案

#### 方案A: 接受当前简化版本
**优点**: 
- 简单可靠
- 效果强于理论值，玩家体验更好
- 无技术实现难度

**缺点**: 
- 不符合原始算法
- 高等级时效果过强

#### 方案B: 使用近似公式
```yaml
effects:
  - id: potion_effect
    args:
      effect: luck
      level: "floor(5 * (1 - pow(0.8, %level%)))"
      duration: 999999
    triggers:
      - static_20
```

**说明**: 使用数学公式近似模拟迭代效果

#### 方案C: 分段实现
```yaml
effects:
  - id: potion_effect
    args:
      effect: luck
      level: "%level% <= 5 ? %level% : %level% <= 15 ? floor(%level% * 0.6) : 5"
      duration: 999999
    triggers:
      - static_20
```

### 🎯 建议

#### 立即可用
保持当前的简单实现，确保附魔可以正常工作：
```yaml
level: "%level%"
```

#### 长期优化
如果需要更准确的算法，可以：
1. 研究EcoEnchants是否支持复杂数学运算
2. 考虑使用外部脚本或插件实现
3. 创建多个等级的附魔文件

### 🧪 测试当前实现

```bash
# 测试不同等级的万象气息
/ecoenchants give <你的用户名> wanxiang_qixi 1      # 幸运1级
/ecoenchants give <你的用户名> wanxiang_qixi 5      # 幸运5级
/ecoenchants give <你的用户名> wanxiang_qixi 10     # 幸运10级

# 测试挖掘效果
# 使用附魔工具挖掘，观察掉落物数量和质量
```

---

## 🎯 总结

万象气息的迭代算法在EcoEnchants中难以完美实现，当前使用简化版本（幸运等级=附魔等级）作为可用的替代方案。虽然不完全符合原始算法，但提供了类似的幸运度提升效果。
