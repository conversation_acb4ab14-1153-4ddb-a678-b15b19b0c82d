display-name: "光环"
description: "在 &a%radius%&r 范围内的玩家获得 &a%reduction%%&r 免伤"
placeholders:
  reduction: "%level% * 5" #"%level% * 10"
  radius: "3 + %level% * 1" #"3 + %level% * 2"
type: special

targets:
  - chestplate
  - leggings
conflicts: [ ]
rarity: special
max-level: 2

tradeable: false
discoverable: true
enchantable: true

effects:
  - id: add_permanent_holder_in_radius
    args:
      radius: "3 + %level% * 2"
      effects:
        - id: damage_multiplier
          args:
            multiplier: "1 - %level% * 0.1"
          triggers:
            - take_damage
      conditions: []

conditions: [ ]
