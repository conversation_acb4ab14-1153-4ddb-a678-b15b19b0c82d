#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量更新附魔文件的类型和稀有度
根据用户需求分配正确的颜色和获取概率
"""

import os
import re

# 定义附魔分类
enchant_categories = {
    # 限定类附魔 - 红色
    'limited': [
        '万象气息', '混沌威压', '一剑隔世', '绝望之力', '睥睨苍生'
    ],
    
    # 传说类附魔 - 金色
    'legendary': [
        '精进', '万象天引', '永葆之躯', '圣光领域', '普度众生'
    ],
    
    # 史诗类附魔 - 粉色
    'epic': [
        '绑定', '无限耐久', '吸血', '再植', '泰山', '斗转星移', '重伤', 'AOE', 
        '重甲克星', '致盲', '中毒', '虚弱', '减速', '眩晕', '凋零', '钢铁之躯', 
        '新陈代谢', '锻造', '引力', '击飞', '退散', '治愈之光', '利刃', '播种机', 
        '中国制造', '破虚', '矿石洗练', '盾构机', '炸弹箭矢', '不服气来干我呀'
    ],
    
    # 罕见类附魔 - 淡蓝色
    'rare': [
        '吞噬', '横扫千军', '石化皮肤', '牵引', '刺骨', '伤害加强', '激素', 
        '防御加强', '扎刺', '速度加强', '鳃', '夜视', '袋鼠', '猎豹', '岩浆行走', 
        '移动修补', '矿元素亲和', '挖掘机', '破甲', '天罚', '快速装填', '海族克星', 
        '忠诚', '耐性', '煽动', '规劝', '制动', '植物亲和', '土豆播种机', '小麦播种机', 
        '胡萝卜播种机', '甜菜播种机', '青蛙跳', '老六', '夜伏', '蓄力一击', 
        '矿元素亲和2', '潜力爆发', '矿脉联锁', '连跳跳', '超级能量', '节能科技'
    ],
    
    # 诅咒类附魔 - 紫色
    'curse': [
        '寄生虫', '重负', '易碎', '绑定诅咒', '消失诅咒', '烙印诅咒', 
        '劣质品诅咒', '霉运诅咒', '空虚诅咒', '经验修补诅咒'
    ]
}

def update_enchant_file(filename, category):
    """更新单个附魔文件"""
    filepath = f"enchants/{filename}.yml"
    if not os.path.exists(filepath):
        print(f"文件不存在: {filepath}")
        return
    
    # 读取文件内容
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 根据分类设置类型和稀有度
    if category == 'limited':
        new_type = 'limited_type'
        new_rarity = 'limited'
    elif category == 'legendary':
        new_type = 'legendary_type'
        new_rarity = 'legendary'
    elif category == 'epic':
        new_type = 'epic_type'
        new_rarity = 'epic'
    elif category == 'rare':
        new_type = 'rare_type'
        new_rarity = 'rare'
    elif category == 'curse':
        new_type = 'curse_type'
        new_rarity = 'curse'
    else:
        print(f"未知分类: {category}")
        return
    
    # 更新type字段
    content = re.sub(r'^type:\s*\w+', f'type: {new_type}', content, flags=re.MULTILINE)
    
    # 更新rarity字段
    content = re.sub(r'^rarity:\s*\w+', f'rarity: {new_rarity}', content, flags=re.MULTILINE)
    
    # 写回文件
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已更新: {filename} -> {category} ({new_type}, {new_rarity})")

def main():
    """主函数"""
    print("开始批量更新附魔文件...")
    
    total_updated = 0
    
    for category, enchant_list in enchant_categories.items():
        print(f"\n更新 {category} 类附魔:")
        for enchant_name in enchant_list:
            update_enchant_file(enchant_name, category)
            total_updated += 1
    
    print(f"\n批量更新完成！共更新了 {total_updated} 个附魔文件。")
    print("\n颜色分配:")
    print("- 限定类: 红色 (&c)")
    print("- 传说类: 金色 (&6)")
    print("- 史诗类: 粉色 (&d)")
    print("- 罕见类: 淡蓝色 (&b)")
    print("- 诅咒类: 紫色 (&5)")

if __name__ == "__main__":
    main()
