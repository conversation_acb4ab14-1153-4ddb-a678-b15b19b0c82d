display-name: "速度加强"
description: "速度增加50%，但是你会额外受到&c%damage_taken%%&r的伤害，该效果不可叠加"
placeholders:
  damage_taken: "50 - (%level% - 1) * 2"
type: rare_type

targets:
  - boots
conflicts: []
rarity: rare
max-level: 20

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: add_potion_effect
    args:
      effect: speed
      amplifier: 1
      duration: 999999
    triggers:
      - hold
    conditions: []
  
  - id: damage_multiplier
    args:
      multiplier: "1.5 - (%level% - 1) * 0.02"
    triggers:
      - take_damage
    conditions: []

conditions: []
