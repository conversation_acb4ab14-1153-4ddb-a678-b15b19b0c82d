display-name: "AOE"
description:
  - "箭矢对一个实体造成伤害时"
  - "会对周围敌人造成&a%damage%%&r的伤害"
  - "伤害半径为&a%radius%&r格"
placeholders:
  damage: "40 + (%level% - 1) * 3"
  radius: "3 + (%level% / 10) * 2"
type: normal

targets:
  - bow
  - crossbow
conflicts: []
rarity: rare
max-level: 20

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: damage_nearby_entities
    args:
      damage: "(%damage% / 100) * %final_damage%"
      radius: "3 + (%level% / 10) * 2"
      damage_as_player: true
    triggers:
      - bow_attack

conditions: []
