display-name: "脉络"
description: "一次连锁采集 &a%placeholder%&r 个矿物方块"
placeholder: "2 + 3 * %level%"
type: rare_type

targets:
  - pickaxe
conflicts: []
rarity: rare
max-level: 4

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: mine_vein
    args:
      limit: "2 + 3 * %level%"
    filters:
      blocks:
        - coal_ore
        - iron_ore
        - copper_ore
        - gold_ore
        - diamond_ore
        - redstone_ore
        - lapis_ore
        - deepslate_coal_ore
        - deepslate_iron_ore
        - deepslate_copper_ore
        - deepslate_gold_ore
        - deepslate_diamond_ore
        - deepslate_redstone_ore
        - deepslate_lapis_ore
    triggers:
      - mine_block

conditions: []
