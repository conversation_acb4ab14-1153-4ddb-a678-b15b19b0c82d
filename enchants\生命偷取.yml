display-name: "生命偷取"
description: "%chance%%概率偷取造成伤害的 &a%health%%&r 为自己血量"
placeholders:
  health: "%level% * 3"
  chance: "5*%level%"
type: normal

targets:
  - sword
conflicts: []
rarity: rare
max-level: 6

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: give_health
    args:
      chance: "5*%level%"
      amount: "%v% * (%level% * 0.03)"
    triggers:
      - melee_attack

conditions: []
