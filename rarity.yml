# 稀有度配置 - 实现用户要求的颜色和概率系统
# 根据用户需求配置6种颜色分类

rarities:
  # 普通类 - 白色 (原版附魔，突破等级变绿色)
  - id: common
    display-name: "&f普通"
    table-chance: 30
    minimum-level: 1
    villager-chance: 10.5
    loot-chance: 12

  # 罕见类 - 淡蓝色 (5%获取概率)
  - id: uncommon
    display-name: "&b罕见"
    table-chance: 5
    minimum-level: 5
    villager-chance: 8
    loot-chance: 12

  # 史诗类 - 粉色 (0.1%获取概率)
  - id: rare
    display-name: "&d史诗"
    table-chance: 0.1
    minimum-level: 15
    villager-chance: 5
    loot-chance: 15

  # 传说类 - 金色 (仅购买获得)
  - id: epic
    display-name: "&6传说"
    table-chance: 0
    minimum-level: 20
    villager-chance: 0
    loot-chance: 0

  # 限定类 - 红色 (仅活动获得，无等级上限)
  - id: legendary
    display-name: "&c限定"
    table-chance: 0
    minimum-level: 25
    villager-chance: 0
    loot-chance: 0

  # 诅咒类 - 紫色 (负面效果，附带获得)
  - id: special
    display-name: "&5诅咒"
    table-chance: 0
    minimum-level: 1
    villager-chance: 0
    loot-chance: 0
