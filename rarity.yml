# 稀有度配置 - 基于EcoEnchants标准语法
# 实现获取概率系统

rarities:
  # 普通稀有度 - 原版附魔
  - id: common
    display-name: "普通"
    table-chance: 50
    minimum-level: 1
    villager-chance: 30
    loot-chance: 40

  # 罕见稀有度 - 5%获取概率
  - id: rare
    display-name: "罕见"
    table-chance: 25   # 临时提高便于测试
    minimum-level: 5
    villager-chance: 15
    loot-chance: 20

  # 史诗稀有度 - 0.1%获取概率
  - id: epic
    display-name: "史诗"
    table-chance: 15  # 临时提高便于测试
    minimum-level: 10
    villager-chance: 8
    loot-chance: 12

  # 传说稀有度 - 仅购买获得
  - id: legendary
    display-name: "传说"
    table-chance: 8   # 临时启用便于测试
    minimum-level: 15
    villager-chance: 5
    loot-chance: 6

  # 限定稀有度 - 仅活动获得
  - id: limited
    display-name: "限定"
    table-chance: 3   # 临时启用便于测试
    minimum-level: 20
    villager-chance: 2
    loot-chance: 3

  # 诅咒稀有度 - 负面效果
  - id: curse
    display-name: "诅咒"
    table-chance: 10   # 临时启用便于测试
    minimum-level: 1
    villager-chance: 5
    loot-chance: 8
