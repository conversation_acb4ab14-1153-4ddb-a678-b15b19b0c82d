# 稀有度配置 - 基于正确EcoEnchants语法实现用户需求
# 通过稀有度控制颜色和获取概率

rarities:
  # 普通稀有度 - 白色 (原版附魔)
  - id: common
    display-name: "普通"
    table-chance: 30
    minimum-level: 1
    villager-chance: 10.5
    loot-chance: 12

  # 不凡稀有度 - 淡蓝色 (罕见类附魔)
  - id: uncommon
    display-name: "罕见"
    table-chance: 15  # 临时提高便于测试，最终改为5
    minimum-level: 5
    villager-chance: 8
    loot-chance: 12

  # 稀有稀有度 - 史诗类附魔
  - id: rare
    display-name: "史诗"
    table-chance: 8   # 临时提高便于测试，最终改为0.1
    minimum-level: 15
    villager-chance: 5
    loot-chance: 15

  # 史诗稀有度 - 传说类附魔
  - id: epic
    display-name: "传说"
    table-chance: 3   # 临时启用便于测试，最终改为0
    minimum-level: 20
    villager-chance: 2
    loot-chance: 8

  # 传说稀有度 - 限定类附魔
  - id: legendary
    display-name: "限定"
    table-chance: 1   # 临时启用便于测试，最终改为0
    minimum-level: 25
    villager-chance: 1
    loot-chance: 3

  # 稀世稀有度 - 特殊附魔
  - id: special
    display-name: "稀世"
    table-chance: 2
    minimum-level: 30
    villager-chance: 1
    loot-chance: 5

  # 绝世稀有度 - 最高级附魔
  - id: veryspecial
    display-name: "绝世"
    table-chance: 1
    minimum-level: 30
    villager-chance: 0.1
    loot-chance: 2
