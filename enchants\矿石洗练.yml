display-name: "矿石洗练"
description: "在挖掘石头时有&a%chance%%&r概率掉落随机矿物"
placeholders:
  chance: "10 + (%level% - 1) * 2"
type: epic_type

targets:
  - pickaxe
conflicts: []
rarity: epic
max-level: 20

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: drop_random_ore
    args:
      chance: "10 + (%level% - 1) * 2"
    triggers:
      - mine_block
    conditions:
      - "%block% is stone"

conditions: []
