display-name: "防御加强"
description: "防御值增加50%，但是你会减少&c%damage_reduce%%&r的伤害，该效果不可叠加"
placeholders:
  damage_reduce: "70 - (%level% - 1) * 2"
type: rare_type

targets:
  - helmet
  - chestplate
  - leggings
  - boots
conflicts: []
rarity: rare
max-level: 20

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: armor_multiplier
    args:
      multiplier: 1.5
    triggers:
      - take_damage
    conditions: []
  
  - id: damage_multiplier
    args:
      multiplier: "0.3 + (%level% - 1) * 0.02"
    triggers:
      - take_damage
    conditions: []

conditions: []
