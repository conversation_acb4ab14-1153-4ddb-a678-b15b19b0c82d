display-name: "奥术防御"
description: "&a%placeholder%%&r 概率免疫药水伤害"
placeholder: "%level% * 4"
type: normal

targets:
  - armor
conflicts: [ ]
rarity: epic
max-level: 5

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: cancel_event
    args:
      chance: "%level% * 4"
    triggers:
      - take_damage
    filters:
      damageCause:
        - poison
        - magic

conditions: [ ]
