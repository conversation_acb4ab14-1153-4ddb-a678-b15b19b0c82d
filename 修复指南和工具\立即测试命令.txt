# EcoEnchants 立即测试命令 - 验证修复效果

## 🔄 第一步：重启服务器
# 重启服务器以加载修复后的附魔文件

## 🧪 第二步：基础功能测试
/ecoenchants reload
/ecoenchants version
/plugins

## 📋 第三步：测试修复后的附魔
# 限定类附魔 (红色) - 应该正常加载
/ecoenchants give <玩家> wanxiang_qixi 5
/ecoenchants give <玩家> hundun_weiya 3
/ecoenchants give <玩家> yijian_geshi 2
/ecoenchants give <玩家> juewang_zhili 4
/ecoenchants give <玩家> pini_cangsheng 3

# 传说类附魔 (金色) - 应该正常加载
/ecoenchants give <玩家> jingjin 3
/ecoenchants give <玩家> wanxiang_tianyin 2
/ecoenchants give <玩家> yongbao_zhiqu 5
/ecoenchants give <玩家> shengguang_lingyu 4
/ecoenchants give <玩家> pudu_zhongsheng 3

# 史诗类附魔 (粉色) - 应该正常加载
/ecoenchants give <玩家> binding 1
/ecoenchants give <玩家> infinite_durability 1
/ecoenchants give <玩家> lifesteal 5
/ecoenchants give <玩家> aoe 10

# 罕见类附魔 (淡蓝色) - 应该正常加载
/ecoenchants give <玩家> devour 3

# 诅咒类附魔 (紫色) - 应该正常加载
/ecoenchants give <玩家> parasite 2
/ecoenchants give <玩家> curse_binding 1

## 🎮 第四步：GUI和功能测试
/ecoenchants gui
/ecoenchants list

## 📊 第五步：检查附魔信息
/ecoenchants info wanxiang_qixi
/ecoenchants info jingjin
/ecoenchants info binding
/ecoenchants info devour
/ecoenchants info parasite

## ✅ 成功标准
- [ ] 服务器启动无错误信息
- [ ] 所有附魔命令执行成功
- [ ] GUI显示附魔列表
- [ ] 附魔书显示正确的中文名称
- [ ] 颜色显示正确：
  * 限定类：红色 (&c)
  * 传说类：金色 (&6)
  * 史诗类：粉色 (&d)
  * 罕见类：淡蓝色 (&b)
  * 诅咒类：紫色 (&5)
- [ ] 附魔效果正常工作

## 🔧 效果测试
# 测试具体附魔效果是否生效

# 测试吸血效果
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:lifesteal",lvl:5}]} 1

# 测试绑定效果
/give <玩家> diamond_helmet{Enchantments:[{id:"ecoenchants:binding",lvl:1}]} 1

# 测试吞噬效果
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:devour",lvl:3}]} 1

# 测试AOE效果
/give <玩家> bow{Enchantments:[{id:"ecoenchants:aoe",lvl:5}]} 1

## 🚨 如果仍有问题
1. 检查服务器控制台错误信息
2. 确认所有文件名符合 [a-z0-9_] 规范
3. 验证YAML语法正确性
4. 检查是否还有ID冲突

## 📝 下一步
如果基础测试成功：
1. 继续创建剩余的附魔文件
2. 实现完整的92个附魔系统
3. 配置诅咒附带机制
4. 优化获取概率和效果平衡

## 🎯 预期结果
修复完成后应该看到：
- 服务器启动时无任何错误信息
- 所有附魔正常加载和注册
- 附魔效果在游戏中正常工作
- 颜色分类完全符合用户需求
- GUI显示完整的附魔列表
