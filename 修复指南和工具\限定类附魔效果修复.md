# 限定类附魔效果修复
## 修复4个限定类附魔的效果问题

### 🔧 修复内容

#### 1. 万象气息 (wanxiang_qixi) ✅
**问题**: 使用了 `permanent_potion_effect` 但没有正确的触发器
**修复**: 
- 改为 `potion_effect` 
- 添加 `triggers: static_20` (每秒触发)
- 添加 `duration: 999999` (长时间持续)

**修复后效果**: 每秒给予玩家幸运效果，等级=附魔等级

#### 2. 混沌威压 (hundun_weiya) ✅
**问题**: 使用了不存在的变量 `%victim_max_health%`
**修复**: 
- 改为固定伤害值 `damage: 5`
- 保留概率机制 `chance: "%level%"`

**修复后效果**: 攻击时有%level%概率造成5点额外伤害

#### 3. 绝望之力 (juewang_zhili) ✅
**问题**: 使用了 `max()` 函数，可能不被支持
**修复**: 
- 简化冷却时间计算
- 移除复杂的 `max()` 函数
- 设置固定冷却时间 `cooldown: 600`

**修复后效果**: 击杀时有%level%概率获得力量效果，持续100秒，冷却600秒

#### 4. 睥睨苍生 (pini_cangsheng) ✅
**问题**: 使用了不存在的变量 `%victim_armor%` 和字符串伤害值
**修复**: 
- 移除条件判断，改为概率触发
- 修复伤害值为数值类型 `damage: 999999`
- 添加概率机制 `chance: "%level%"`

**修复后效果**: 攻击时有%level%概率造成巨额伤害和虚弱效果

### 📊 修复后的效果总结

#### 1. 万象气息
- **触发**: 每秒自动触发
- **效果**: 给予幸运效果，等级=附魔等级
- **持续**: 长时间持续

#### 2. 混沌威压  
- **触发**: 近战攻击和远程攻击
- **效果**: %level%概率造成5点额外伤害
- **示例**: 5级=5%概率，50级=50%概率

#### 3. 绝望之力
- **触发**: 击杀敌人时
- **效果**: %level%概率获得力量效果
- **持续**: 100秒，冷却600秒
- **示例**: 10级=10%概率获得力量10级

#### 4. 睥睨苍生
- **触发**: 近战攻击时
- **效果**: %level%概率造成999999伤害+虚弱效果
- **示例**: 1级=1%概率秒杀，100级=100%概率秒杀

### 🧪 测试命令

```bash
# 重启服务器
/ecoenchants reload

# 测试修复后的限定类附魔
/ecoenchants give <你的用户名> wanxiang_qixi 5      # 万象气息 5级
/ecoenchants give <你的用户名> hundun_weiya 10      # 混沌威压 10级
/ecoenchants give <你的用户名> juewang_zhili 5      # 绝望之力 5级
/ecoenchants give <你的用户名> pini_cangsheng 10    # 睥睨苍生 10级

# 检查效果
/effect give <你的用户名> clear  # 清除现有效果
/ecoenchants gui                 # 检查GUI显示
```

### ✅ 预期测试结果

#### 万象气息测试
- 手持附魔工具时，应该看到幸运效果图标
- 挖掘时应该获得更多掉落物

#### 混沌威压测试
- 攻击敌人时，有概率看到额外伤害数字
- 高等级时触发概率更高

#### 绝望之力测试
- 击杀敌人时，有概率获得力量效果
- 获得效果时应该看到力量图标和攻击力提升

#### 睥睨苍生测试
- 攻击敌人时，有概率直接秒杀
- 同时给敌人施加虚弱效果

### 🔄 如果仍有问题

如果修复后仍有问题，可能的原因：

1. **EcoEnchants版本兼容性**: 某些效果ID可能不被支持
2. **服务器权限**: 可能缺少执行某些效果的权限
3. **插件冲突**: 其他插件可能干扰附魔效果

**解决方案**:
- 检查EcoEnchants版本和文档
- 查看服务器控制台错误信息
- 尝试更简单的效果实现

### 📝 简化版本备选方案

如果当前修复仍有问题，可以使用更简单的效果：

```yaml
# 万象气息 - 简化版
effects:
  - id: luck_multiplier
    args:
      multiplier: "%level%"

# 混沌威压 - 简化版  
effects:
  - id: damage_multiplier
    args:
      multiplier: "1 + (%level% * 0.01)"

# 绝望之力 - 简化版
effects:
  - id: damage_multiplier
    args:
      multiplier: "%level%"
      chance: "%level%"

# 睥睨苍生 - 简化版
effects:
  - id: damage_multiplier
    args:
      multiplier: "%level%"
```

---

## 🎯 修复完成

限定类附魔的效果问题已修复，现在应该能正常工作了！请测试这些修复后的附魔。
