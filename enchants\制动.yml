display-name: "制动"
description: "受到碰撞的伤害降低&a%reduction%%&r。效果不可叠加"
placeholders:
  reduction: "5 + (%level% - 1) * 3"
type: rare_type

targets:
  - helmet
  - chestplate
  - leggings
  - boots
conflicts: []
rarity: rare
max-level: 20

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: damage_multiplier
    args:
      multiplier: "1 - (0.05 + (%level% - 1) * 0.03)"
    triggers:
      - take_damage
    conditions:
      - "%damage_cause% = fall"

conditions: []
