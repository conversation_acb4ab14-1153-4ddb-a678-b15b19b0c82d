display-name: "地狱领主"
description: "增加在地狱 &a%placeholder%% &r的伤害"
placeholder: "20 + %level% * 2" #"20 + %level% * 10"
type: normal

targets:
  - sword
  - bow
  - trident
conflicts:
  - end_affinity
rarity: epic
max-level: 4

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: damage_multiplier
    args:
      multiplier: "1.2 + %level% * 0.1"
    triggers:
      - melee_attack
      - bow_attack
      - trident_attack

conditions:
  - id: in_world
    args:
      world: world_nether
