display-name: "霜冻"
description: "&a%chance%%&r 概率让目标冻结 &a%seconds%&r 秒"
placeholders:
  chance: "3 + 2 * %level%"
  seconds: "2 + %level%"
type: normal

targets:
  - bow
conflicts: [ ]
rarity: legendary
max-level: 3

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: set_freeze_ticks
    args:
      ticks: "60 + 20 * %level%"
      chance: "3 + 2 * %level%"
    triggers:
      - bow_attack

conditions: [ ]
