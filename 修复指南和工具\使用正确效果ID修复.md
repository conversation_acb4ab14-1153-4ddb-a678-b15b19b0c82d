# 使用正确效果ID修复限定类附魔
## 确认%level%变量存在，使用正确的效果ID

### ✅ 变量确认

从EcoEnchants配置文档中确认：
- **%level%变量存在** ✅ - 表示附魔等级
- **常用效果ID**: `damage_multiplier`, `potion_effect`, `xp_multiplier` 等
- **触发器**: `melee_attack`, `bow_attack`, `static_20` 等

### 🔧 使用正确效果ID修复

#### 1. 万象气息 ✅
**原本效果**: 幸运度迭代增长
**修复方案**: 
- 使用 `potion_effect` + `LUCK`
- 设置 `level: "%level%"` (幸运等级=附魔等级)
- 使用 `static_20` 触发器 (每秒触发)
- 设置长时间持续 `duration: 999999`

**效果**: 给予玩家幸运效果，等级随附魔等级增长

#### 2. 混沌威压 ✅
**原本效果**: %level%概率造成25%最大生命值伤害
**修复方案**: 
- 使用 `damage_multiplier` 
- 设置 `multiplier: 1.25` (125%伤害，相当于额外25%)
- 设置 `chance: "%level%"` (概率=附魔等级%)

**效果**: 攻击时有%level%概率造成125%伤害(额外25%伤害)

#### 3. 绝望之力 ✅
**原本效果**: %level%概率触发，增加%level%倍攻击力
**修复方案**: 
- 保持 `damage_multiplier`
- 设置 `multiplier: "1 + %level%"` (1倍基础+等级倍数)
- 保持 `chance: "%level%"` 和 `duration: 2000`

**效果**: 击杀时有%level%概率获得(1+%level%)倍攻击力

#### 4. 睥睨苍生 ✅
**原本效果**: 削弱护甲+低护甲秒杀
**修复方案**: 
- 使用 `damage_multiplier` 实现高伤害
- 设置 `multiplier: "%level%"` (伤害倍数=附魔等级)
- 添加虚弱效果削弱敌人
- 设置 `chance: "%level%"` (概率=附魔等级%)

**效果**: 攻击时有%level%概率造成%level%倍伤害+虚弱效果

### 📊 修复后的效果说明

#### 万象气息
- **1级**: 给予幸运1级效果
- **5级**: 给予幸运5级效果  
- **10级**: 给予幸运10级效果
- **效果**: 挖掘时获得更多更好的掉落物

#### 混沌威压
- **1级**: 1%概率造成125%伤害
- **5级**: 5%概率造成125%伤害
- **10级**: 10%概率造成125%伤害
- **效果**: 相当于额外25%伤害的概率触发

#### 绝望之力
- **1级**: 1%概率获得2倍攻击力(1+1)
- **5级**: 5%概率获得6倍攻击力(1+5)
- **10级**: 10%概率获得11倍攻击力(1+10)
- **效果**: 击杀后获得巨幅攻击力提升

#### 睥睨苍生
- **1级**: 1%概率造成1倍伤害+虚弱1级
- **10级**: 10%概率造成10倍伤害+虚弱10级
- **50级**: 50%概率造成50倍伤害+虚弱50级
- **效果**: 高概率高倍数伤害+削弱敌人

### 🧪 测试命令

```bash
# 重启服务器
/ecoenchants reload

# 测试修复后的限定类附魔
/ecoenchants give <你的用户名> wanxiang_qixi 5      # 万象气息 5级
/ecoenchants give <你的用户名> hundun_weiya 10      # 混沌威压 10级
/ecoenchants give <你的用户名> juewang_zhili 5      # 绝望之力 5级
/ecoenchants give <你的用户名> pini_cangsheng 20    # 睥睨苍生 20级

# 检查效果
/effect give <你的用户名> clear  # 清除现有效果
/ecoenchants gui                 # 检查GUI显示
```

### ✅ 预期测试结果

#### 万象气息测试
- 手持附魔工具时，应该看到幸运效果图标
- 挖掘石头、矿物时获得更多掉落物
- 钓鱼时获得更好的物品

#### 混沌威压测试
- 攻击敌人时，有概率看到更高的伤害数字
- 10级时有10%概率触发125%伤害

#### 绝望之力测试
- 击杀敌人时，有概率获得攻击力提升效果
- 5级时有5%概率获得6倍攻击力，持续100秒

#### 睥睨苍生测试
- 攻击敌人时，有概率造成巨额伤害
- 20级时有20%概率造成20倍伤害
- 同时给敌人施加虚弱20级效果

### 🎯 效果验证方法

1. **万象气息**: 挖掘大量石头，观察掉落物数量
2. **混沌威压**: 攻击高血量敌人，观察伤害数字
3. **绝望之力**: 击杀大量敌人，观察攻击力提升
4. **睥睨苍生**: 攻击敌人，观察高伤害和虚弱效果

---

## 🎯 总结

现在使用的都是EcoEnchants确实支持的效果ID和变量，%level%变量确实存在！修复后的附魔应该能正常工作了。
