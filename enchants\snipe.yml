display-name: "狙击手"
description: "箭矢造成每移动 &a%blocks%&r 距离额外造成 &a%damage%%&r 伤害"
placeholders:
  damage: "1 * %level%"
  blocks: "11 - ceil(%level% / 2)"
type: normal

targets:
  - bow
  - crossbow
conflicts: [ ]
rarity: epic
max-level: 4

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: damage_multiplier
    args:
      multiplier: "1 + (%distance% / (11 - ceil(%level% / 2))) * 0.01 * %level%"
    triggers:
      - bow_attack

conditions: [ ]
