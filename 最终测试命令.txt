# EcoEnchants 最终测试命令
# 全面修复后的测试方案

## 🔄 第一步：基础检查
/ecoenchants reload
/ecoenchants version
/plugins
/lp user <你的用户名> permission set ecoenchants.* true

## 🧪 第二步：直接给予测试（确保基础功能正常）

### 罕见类附魔测试 (淡蓝色)
/ecoenchants give <你的用户名> 心灵遥感 1
/ecoenchants give <你的用户名> 脉络 2
/ecoenchants give <你的用户名> 吞噬 3

### 史诗类附魔测试 (粉色)
/ecoenchants give <你的用户名> 生命偷取 3
/ecoenchants give <你的用户名> 绑定 1
/ecoenchants give <你的用户名> 无限耐久 1
/ecoenchants give <你的用户名> 吸血 5

### 传说类附魔测试 (金色)
/ecoenchants give <你的用户名> 智慧 3

### 限定类附魔测试 (红色)
/ecoenchants give <你的用户名> 幸运光环 5

### 诅咒类附魔测试 (紫色)
/ecoenchants give <你的用户名> 饥饿诅咒 2

## 🎮 第三步：GUI测试
/ecoenchants gui
# 检查是否显示附魔列表，颜色是否正确

## 📋 第四步：附魔信息检查
/ecoenchants info 心灵遥感
/ecoenchants info 生命偷取
/ecoenchants info 智慧
/ecoenchants info 幸运光环
/ecoenchants info 饥饿诅咒

## 🔧 第五步：附魔台测试
/give <你的用户名> enchanting_table 1
/give <你的用户名> lapis_lazuli 64
/give <你的用户名> experience_bottle 32
/give <你的用户名> diamond_sword 3
/give <你的用户名> diamond_pickaxe 3
/give <你的用户名> book 10

# 然后用附魔台尝试附魔，看是否出现自定义附魔

## 🎯 第六步：功能效果测试

### 测试心灵遥感
# 用附有心灵遥感的镐子挖掘，看掉落物是否直接进背包

### 测试生命偷取
# 用附有生命偷取的剑攻击怪物，看是否恢复生命值

### 测试脉络
# 用附有脉络的镐子挖掘矿物，看是否连锁挖掘

### 测试绑定
# 装备附有绑定的物品后死亡，看是否不掉落

### 测试无限耐久
# 使用附有无限耐久的工具，看耐久是否不减少

### 测试智慧
# 手持附有智慧的工具获取经验，看是否有倍率加成

### 测试幸运光环
# 装备附有幸运光环的工具，检查是否有幸运效果

### 测试饥饿诅咒
# 装备附有饥饿诅咒的物品，看饥饿消耗是否增加

## ✅ 成功标准

### 基础功能正常：
- [ ] 命令给予附魔书成功
- [ ] 附魔书显示正确的颜色
- [ ] GUI显示附魔列表
- [ ] 附魔台能获取自定义附魔

### 颜色显示正确：
- [ ] 罕见类显示淡蓝色 (&b)
- [ ] 史诗类显示粉色 (&d)
- [ ] 传说类显示金色 (&6)
- [ ] 限定类显示红色 (&c)
- [ ] 诅咒类显示紫色 (&5)

### 功能效果正常：
- [ ] 心灵遥感：掉落物直接进背包
- [ ] 生命偷取：攻击恢复生命值
- [ ] 脉络：连锁挖掘矿物
- [ ] 绑定：死亡不掉落
- [ ] 无限耐久：耐久不减少
- [ ] 智慧：经验倍率加成
- [ ] 幸运光环：幸运效果
- [ ] 饥饿诅咒：饥饿消耗增加

## 🚨 如果出现问题

### 命令无法执行：
1. 检查权限：/lp user <你的用户名> permission check ecoenchants.*
2. 检查插件状态：/plugins (确保EcoEnchants是绿色)
3. 查看控制台错误信息

### GUI显示空白：
1. 重载插件：/ecoenchants reload
2. 检查配置文件语法
3. 确认附魔文件中 discoverable: true

### 附魔台没有自定义附魔：
1. 检查 table-chance 是否大于0
2. 确认有足够的经验等级
3. 多次尝试（概率性获取）

### 附魔效果不生效：
1. 检查控制台错误信息
2. 确认Paper版本兼容性
3. 检查effects配置语法

## 📞 反馈信息

请测试完成后告诉我：

1. **哪些命令成功了？**
2. **哪些命令失败了？**
3. **GUI显示什么？**
4. **控制台有什么错误信息？**
5. **附魔效果是否正常工作？**

根据你的反馈，我可以进一步优化和修复！

---

## 🎉 如果一切正常

恭喜！EcoEnchants插件已经修复完成。接下来可以：

1. **恢复正确的获取概率** - 将测试概率调整为最终需要的概率
2. **添加更多附魔** - 基于已修复的模板创建更多附魔
3. **优化平衡性** - 调整附魔效果和等级
4. **完善功能** - 添加更多高级功能

**现在开始测试吧！** 🚀
