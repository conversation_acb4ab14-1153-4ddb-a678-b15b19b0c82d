#
# EcoEnchants
# by Auxilor
#

enchant-gui:
  enchant-area:
    width: 7
    column: 2
    row: 3
    height: 3
  empty-item: gray_stained_glass_pane name:""
  custom-slots: []
  rows: 6
  title: 附魔 GUI
  item-column: 5
  item-row: 1
  page-change:
    forwards:
      item: arrow name:"&f下一页"
      column: 6
      row: 6
    backwards:
      item: arrow name:"&f上一页"
      column: 4
      row: 6
  mask:
    pattern:
    - '111101111'
    - '111111111'
    - '100000001'
    - '100000001'
    - '100000001'
    - '111111111'
    items:
    - black_stained_glass_pane
  info:
    item: player_head name:"&a我该如何使用这个?" texture:eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvMjcwNWZkOTRhMGM0MzE5MjdmYjRlNjM5YjBmY2ZiNDk3MTdlNDEyMjg1YTAyYjQzOWUwMTEyZGEyMmIyZTJlYyJ9fX0=
    lore:
    - '&f在顶部放置一个物品,'
    - '&f所有你能为此物品添'
    - '&f加的附魔都会显示在'
    - '&f这个界面的下方区域!'
    column: 9
    row: 1
villager:
  pass-through-chance: 25
  book-multiplier: 0.14
  reduction: 5
  enabled: true
enable-1-20-6: false
anvil:
  cost-exponent: 1
  use-rework-penalty: true
  enchant-limit: 12
  max-repair-cost: 40
display:
  above-max-level:
    level-only: false
    format: "&a" # 绿色 - 当附魔等级超过原版上限时显示
    enabled: true
  numerals:
    threshold: 10
    enabled: true
  sort:
    rarity-order:
    - common
    - rare
    - epic
    - legendary
    - limited
    - curse
    length: false
    type: true
    type-order:
    - normal
    - normal_enhanced
    - rare_type
    - epic_type
    - legendary_type
    - limited_type
    - curse_type
    rarity: true
  descriptions:
    word-wrap: 27
    format: '&8'
    threshold: 5
    enabled: true
  enabled: true
  not-met:
    format: <strikethrough>
  require-enchantable: true
  collapse:
    delimiter: ', '
    threshold: 9
    per-line: 2
    enabled: true
lore-conversion:
  aggressive: false
  enabled: false
enchanting-table:
  cap: 5
  book-multiplier: 0.5
  reduction: 2.2
  enabled: true
  maximum-obtainable-level: 30
enchantinfo:
  item:
    lore:
    - ''
    - '&f最高等级: &a%max_level%'
    - '&f稀有指数: &a%rarity%'
    - '&f适用物品: &a%targets%'
    - '&f冲突附魔: &a%conflicts%'
    column: 5
    row: 2
    show-max-level: true
  custom-slots: []
  rows: 3
  mask:
    pattern:
    - '111111111'
    - '111101111'
    - '111111111'
    items:
    - black_stained_glass_pane
loot:
  book-multiplier: 0.5
  reduction: 7.5
  enabled: true

