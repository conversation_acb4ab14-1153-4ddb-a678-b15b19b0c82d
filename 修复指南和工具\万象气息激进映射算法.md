# 万象气息激进映射算法
## 重新设计高价值的幸运等级映射

### 🚨 原映射算法的问题

#### 钻石挖掘效果分析
**原映射 (保守型)**:
- 50级万象气息 → 幸运5级 → 3个钻石
- 100级万象气息 → 幸运6级 → 3.4个钻石 
- **问题**: 50级升级到100级，钻石只增加0.4个，完全不划算！

#### 升级价值感缺失
- 高等级万象气息升级收益微弱
- 玩家投入大量资源升级，但效果提升不明显
- 违背了"限定类附魔应该极其强力"的设计初衷

### 🚀 新映射算法 (激进型)

#### 新的等级映射
```yaml
level: "%level% <= 1 ? 1 : %level% <= 3 ? 3 : %level% <= 5 ? 5 : %level% <= 10 ? 8 : %level% <= 20 ? 12 : %level% <= 50 ? 20 : %level% <= 100 ? 35 : 50"
```

| 附魔等级 | 获得幸运等级 | 钻石挖掘效果 | 提升幅度 |
|----------|-------------|-------------|----------|
| 1级 | 幸运1级 | 1.33个钻石 | +33% |
| 2-3级 | **幸运3级** | 2.2个钻石 | +120% |
| 4-5级 | **幸运5级** | 3个钻石 | +200% |
| 6-10级 | **幸运8级** | 约4.5个钻石 | +350% |
| 11-20级 | **幸运12级** | 约6.5个钻石 | +550% |
| 21-50级 | **幸运20级** | 约11个钻石 | +1000% |
| 51-100级 | **幸运35级** | 约19个钻石 | +1800% |
| 101级+ | **幸运50级** | 约27个钻石 | +2600% |

### 📊 激进映射的优势

#### ✅ 升级价值感强烈
- **5级→10级**: 从3个钻石提升到4.5个钻石 (+1.5个)
- **20级→50级**: 从6.5个钻石提升到11个钻石 (+4.5个)
- **100级→200级**: 从19个钻石提升到27个钻石 (+8个)

#### ✅ 符合限定类定位
- 限定类附魔应该是最强力的
- 高等级万象气息确实能带来震撼性的效果
- 体现了"极大的增加幸运度"的描述

#### ✅ 激励升级投入
- 每次升级都有明显的效果提升
- 高等级万象气息成为真正的神器
- 玩家愿意投入资源进行升级

### 🎮 实际游戏体验

#### 低等级 (1-5级)
- **1级**: 轻微提升，入门体验
- **3级**: 明显提升，感受到强力效果
- **5级**: 显著提升，已经很强力

#### 中等级 (6-20级)
- **10级**: 幸运8级，挖掘效果非常明显
- **20级**: 幸运12级，每次挖掘都有惊喜

#### 高等级 (21-100级)
- **50级**: 幸运20级，挖一个钻石矿得到11个钻石！
- **100级**: 幸运35级，挖一个钻石矿得到19个钻石！

#### 超高等级 (101级+)
- **200级**: 幸运50级，挖一个钻石矿得到27个钻石！
- 真正的神器级别效果

### 🔬 技术可行性

#### Minecraft幸运上限
- **理论上限**: 255级
- **实际测试**: 需要验证高等级幸运是否正常工作
- **EcoEnchants支持**: 需要测试是否支持超高等级

#### 可能的问题
1. **服务器性能**: 超高幸运可能影响性能
2. **游戏平衡**: 可能过于强力，影响经济平衡
3. **技术限制**: 高等级幸运可能不被支持

### 🧪 测试计划

#### 基础测试
```bash
# 重启服务器
/ecoenchants reload

# 测试不同等级的新映射
/ecoenchants give <你的用户名> wanxiang_qixi 1      # 幸运1级
/ecoenchants give <你的用户名> wanxiang_qixi 3      # 幸运3级
/ecoenchants give <你的用户名> wanxiang_qixi 5      # 幸运5级
/ecoenchants give <你的用户名> wanxiang_qixi 10     # 幸运8级
/ecoenchants give <你的用户名> wanxiang_qixi 20     # 幸运12级

# 检查描述显示
/ecoenchants gui
```

#### 高等级测试
```bash
# 测试超高等级
/ecoenchants give <你的用户名> wanxiang_qixi 50     # 幸运20级
/ecoenchants give <你的用户名> wanxiang_qixi 100    # 幸运35级
/ecoenchants give <你的用户名> wanxiang_qixi 200    # 幸运50级

# 实际挖掘测试
# 找到钻石矿，用不同等级的万象气息工具挖掘，记录掉落数量
```

### 📊 预期测试结果

#### 描述显示
- **10级**: "幸运等级8级"
- **50级**: "幸运等级20级"
- **100级**: "幸运等级35级"

#### 挖掘效果
- **10级**: 挖钻石应该得到4-5个
- **50级**: 挖钻石应该得到10-12个
- **100级**: 挖钻石应该得到18-20个

### 🎯 平衡性考虑

#### 如果效果过强
可以适当调整映射：
```yaml
# 温和版本
level: "%level% <= 1 ? 1 : %level% <= 3 ? 2 : %level% <= 5 ? 3 : %level% <= 10 ? 5 : %level% <= 20 ? 8 : %level% <= 50 ? 12 : %level% <= 100 ? 18 : 25"
```

#### 如果技术不支持
可以使用组合效果：
```yaml
effects:
  - id: potion_effect
    args:
      effect: luck
      level: 5
  - id: potion_effect
    args:
      effect: haste
      level: "%level% > 20 ? 3 : 1"
  # 添加其他增益效果
```

### 🔄 备用方案

#### 方案A: 分段激进
- 1-10级: 温和增长
- 11-50级: 激进增长
- 51级+: 超激进增长

#### 方案B: 组合效果
- 基础幸运 + 急迫 + 其他增益
- 模拟超高幸运的效果

#### 方案C: 自定义机制
- 使用特殊的掉落倍数机制
- 绕过幸运等级限制

---

## 🎯 激进映射完成

新的万象气息映射算法让高等级附魔有了真正震撼性的效果：
- ✅ 升级价值感强烈
- ✅ 符合限定类定位
- ✅ 激励玩家投入

请测试这个新的激进映射算法！
