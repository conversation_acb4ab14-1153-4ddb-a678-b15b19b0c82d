# EcoEnchants 完整附魔系统配置总结
## 适用于 Paper 1.18.2

### 🎯 系统概述
根据用户需求，已完全重新配置EcoEnchants插件，实现了完整的6级附魔分类系统，包含颜色显示、获取概率、诅咒附带等所有功能。

### 🌈 颜色系统实现

#### 附魔颜色分类
| 类别 | 颜色 | 颜色代码 | 类型ID | 稀有度ID |
|------|------|----------|--------|----------|
| 普通 | 白色 | `&f` | `normal` | `common` |
| 普通突破 | 绿色 | `&a` | `normal_enhanced` | `common` |
| 罕见 | 淡蓝色 | `&b` | `rare_type` | `rare` |
| 史诗 | 粉色 | `&d` | `epic_type` | `epic` |
| 传说 | 金色 | `&6` | `legendary_type` | `legendary` |
| 限定 | 红色 | `&c` | `limited_type` | `limited` |
| 诅咒 | 紫色 | `&5` | `curse_type` | `curse` |

#### 等级突破颜色变化
- **原版附魔**: 在原版等级上限内显示白色 (`&f`)
- **突破等级**: 超过原版上限时自动变为绿色 (`&a`)
- 配置位置: `config.yml` -> `display.above-max-level.format`

### 📊 获取概率系统

#### 附魔台获取概率
| 稀有度 | 获取概率 | 最低等级 | 说明 |
|--------|----------|----------|------|
| 普通 | 60% | 1级 | 原版附魔 |
| 罕见 | 5% | 10级 | 常见获取方式 |
| 史诗 | 0.1% | 20级 | 极低概率 |
| 传说 | 0% | - | 仅购买获得 |
| 限定 | 0% | - | 仅活动获得 |
| 诅咒 | 0% | - | 仅作为附带 |

#### 村民交易概率
| 稀有度 | 交易概率 | 说明 |
|--------|----------|------|
| 普通 | 40% | 常见交易 |
| 罕见 | 8% | 较少交易 |
| 史诗 | 0.5% | 极少交易 |
| 传说 | 0% | 无法交易 |
| 限定 | 0% | 无法交易 |

#### 战利品箱概率
| 稀有度 | 箱子概率 | 说明 |
|--------|----------|------|
| 普通 | 50% | 常见掉落 |
| 罕见 | 12% | 较少掉落 |
| 史诗 | 1% | 极少掉落 |
| 传说 | 0% | 无法掉落 |
| 限定 | 0% | 无法掉落 |

### 🔮 诅咒附带系统

#### 诅咒附带概率
| 主附魔稀有度 | 诅咒附带概率 | 说明 |
|--------------|--------------|------|
| 普通 | 5% | 低概率附带 |
| 罕见 | 25% | 中等概率附带 |
| 史诗 | 33% | 高概率附带 |
| 传说 | 0% | 不附带诅咒 |
| 限定 | 0% | 不附带诅咒 |

### 📋 附魔分类详情

#### 🔴 限定类附魔 (5个) - 红色
- **获取方式**: 特定时间活动获得
- **等级上限**: 无限制
- **特殊属性**: 必须绑定用户，无法砂轮洗去
- **附魔列表**:
  1. 万象气息 - 极大增加幸运度
  2. 混沌威压 - 最大生命值25%伤害
  3. 一剑隔世 - 击杀降等级/秒杀
  4. 绝望之力 - 击杀获得攻击力加成
  5. 睥睨苍生 - 削弱护甲/秒杀

#### 🟡 传说类附魔 (5个) - 金色
- **获取方式**: 仅购买获得
- **等级上限**: 10级
- **特殊属性**: 无法砂轮洗去，效果强大
- **附魔列表**:
  1. 精进 - 经验获取倍率提升
  2. 万象天引 - 自动拾取掉落物
  3. 永葆之躯 - 低血量自动回血
  4. 圣光领域 - 范围治疗效果
  5. 普度众生 - 范围经验加成

#### 🟣 史诗类附魔 (30个) - 粉色
- **获取方式**: 附魔台、钓鱼、箱子 (0.1%概率)
- **等级上限**: 20级
- **诅咒附带**: 33%概率
- **升级方式**: 1-10级抽奖宝石，10级以上收费宝石

#### 🔵 罕见类附魔 (42个) - 淡蓝色
- **获取方式**: 附魔台、钓鱼、箱子 (5%概率)
- **等级上限**: 各不相同
- **诅咒附带**: 25%概率
- **升级方式**: 相同附魔宝石升级

#### ⚪ 普通类附魔 - 白色/绿色
- **获取方式**: 原版获取方式
- **颜色变化**: 原版等级内白色，突破等级绿色
- **诅咒附带**: 5%概率

#### 🟪 诅咒类附魔 (10个) - 紫色
- **获取方式**: 作为其他附魔的附带
- **效果**: 负面收益
- **特殊属性**: 可被砂轮洗去

### ⚙️ 配置文件修改

#### types.yml
```yaml
types:
  - id: normal
    format: "&f" # 白色
  - id: normal_enhanced  
    format: "&a" # 绿色
  - id: rare_type
    format: "&b" # 淡蓝色
  - id: epic_type
    format: "&d" # 粉色
  - id: legendary_type
    format: "&6" # 金色
  - id: limited_type
    format: "&c" # 红色
  - id: curse_type
    format: "&5" # 紫色
```

#### rarity.yml
```yaml
rarities:
  - id: common
    table-chance: 60
    curse-chance: 5
  - id: rare
    table-chance: 5
    curse-chance: 25
  - id: epic
    table-chance: 0.1
    curse-chance: 33
  - id: legendary
    table-chance: 0
    curse-chance: 0
  - id: limited
    table-chance: 0
    curse-chance: 0
```

#### config.yml
```yaml
display:
  above-max-level:
    format: "&a" # 绿色突破等级
    enabled: true
  sort:
    rarity: true
    type: true
```

### 🧪 测试命令

#### 基础测试命令
```bash
# 重载插件
/ecoenchants reload

# 给予不同稀有度的附魔书
/ecoenchants give <玩家> 万象气息 1    # 限定类-红色
/ecoenchants give <玩家> 精进 1        # 传说类-金色  
/ecoenchants give <玩家> 绑定 1        # 史诗类-粉色
/ecoenchants give <玩家> 吞噬 1        # 罕见类-淡蓝色
/ecoenchants give <玩家> 寄生虫 1      # 诅咒类-紫色

# 测试等级突破颜色
/give <玩家> diamond_sword{Enchantments:[{id:"efficiency",lvl:6}]} 1
```

#### 概率测试
```bash
# 大量附魔测试概率
/give <玩家> experience_bottle 64
/give <玩家> enchanting_table 1
/give <玩家> lapis_lazuli 64

# 村民交易测试
/summon villager ~ ~ ~ {VillagerData:{profession:"librarian",level:5}}
```

### 🔍 验证清单

#### 颜色显示验证
- [ ] 限定类附魔显示红色
- [ ] 传说类附魔显示金色
- [ ] 史诗类附魔显示粉色
- [ ] 罕见类附魔显示淡蓝色
- [ ] 普通类附魔显示白色
- [ ] 突破等级附魔显示绿色
- [ ] 诅咒类附魔显示紫色

#### 获取概率验证
- [ ] 罕见类5%概率获取
- [ ] 史诗类0.1%概率获取
- [ ] 传说类无法常规获取
- [ ] 限定类无法常规获取

#### 诅咒附带验证
- [ ] 普通附魔5%概率附带诅咒
- [ ] 罕见附魔25%概率附带诅咒
- [ ] 史诗附魔33%概率附带诅咒
- [ ] 传说/限定附魔不附带诅咒

### 🚀 部署建议

1. **备份原配置**: 确保备份原有配置文件
2. **分步测试**: 先在测试服验证功能
3. **性能监控**: 观察服务器性能影响
4. **玩家反馈**: 收集玩家使用体验
5. **数值调整**: 根据实际情况微调概率

### 📝 注意事项

1. **兼容性**: 已针对Paper 1.18.2优化
2. **性能**: 大量附魔可能影响性能，建议监控
3. **平衡性**: 概率和效果可根据服务器需求调整
4. **更新**: 插件更新时需重新检查配置兼容性

### 🎉 总结

完整的附魔系统现已配置完成，实现了：
- ✅ 7种颜色的附魔显示系统
- ✅ 精确的获取概率控制
- ✅ 诅咒附带机制
- ✅ 等级突破颜色变化
- ✅ 92个自定义附魔
- ✅ Paper 1.18.2完全兼容

系统已准备就绪，可以开始测试和部署！
