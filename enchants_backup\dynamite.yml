display-name: "炸药"
description: "在 &a%placeholder%x%placeholder%&r 区域内挖掘"
placeholder: "%level% * 4"
type: spell

targets:
  - pickaxe
conflicts: []
rarity: legendary
max-level: 2

tradeable: true
discoverable: true
enchantable: true

effects:
  - effects:
    - id: create_explosion
      args:
        power: 0
        amount: "%level% * 2"

    - id: mine_radius
      args:
        radius: "1.5 * %level%"
        blacklisted_blocks:
          - obsidian
          - barrier
          - bedrock
          - chest
        check_hardness: true

    - id: play_sound
      args:
        sound: entity_dragon_fireball_explode
        pitch: 1
        volume: 1

#    - id: break_block

    args:
      cooldown: 90
      send_cooldown_message: true
    triggers:
      - alt_click

conditions: []
