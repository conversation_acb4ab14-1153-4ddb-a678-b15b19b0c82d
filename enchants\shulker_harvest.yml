display-name: "潜影贝收割者"
description: "获得 &a%placeholder%%&r 潜影壳掉落增益"
placeholder: "ceil(((1 / (%level% + 4)) + ((%level% + 3) / 2)) * 100 - 100)"
type: normal

targets:
  - sword
conflicts:
  - looting
rarity: epic
max-level: 3

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: multiply_drops
    args:
      on_items:
        - shulker_shell
      fortune: "2 + %level%"
    triggers:
      - entity_item_drop

conditions: [ ]
