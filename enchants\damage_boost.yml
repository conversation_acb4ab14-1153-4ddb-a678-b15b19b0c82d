display-name: "伤害加强"
description: "伤害增加50%，但是你会额外受到&c%damage_taken%%&r的伤害，该效果不可叠加"
placeholders:
  damage_taken: "70 - (%level% - 1) * 2"
type: rare_type

targets:
  - sword
conflicts: []
rarity: rare
max-level: 20

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: damage_multiplier
    args:
      multiplier: 1.5
    triggers:
      - melee_attack
    conditions: []
  
  - id: damage_multiplier
    args:
      multiplier: "1.7 - (%level% - 1) * 0.02"
    triggers:
      - take_damage
    conditions: []

conditions: []
