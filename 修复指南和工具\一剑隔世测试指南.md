# 一剑隔世附魔测试指南
## 验证降级和秒杀效果

### 🗡️ 一剑隔世逻辑说明

#### 正确的逻辑
- **降级效果**: 当被击杀玩家等级 > 附魔等级时，降低等级
- **秒杀效果**: 当被击杀玩家等级 ≤ 附魔等级时，直接秒杀

#### 示例场景

##### 1级一剑隔世附魔
- **目标玩家等级2**: 击杀后降低1级 → 变成1级
- **目标玩家等级1**: 直接秒杀
- **目标玩家等级0**: 直接秒杀

##### 5级一剑隔世附魔  
- **目标玩家等级10**: 击杀后降低5级 → 变成5级
- **目标玩家等级5**: 直接秒杀
- **目标玩家等级3**: 直接秒杀

##### 50级一剑隔世附魔
- **目标玩家等级100**: 击杀后降低50级 → 变成50级
- **目标玩家等级50**: 直接秒杀
- **目标玩家等级30**: 直接秒杀

### 🔧 修复内容

#### 命令格式修复
- **修复前**: `"xp add %victim% -%level%L"`
- **修复后**: `"experience add %victim% -%level% levels"`

#### 伤害值修复
- **修复前**: `damage: "999999"` (字符串)
- **修复后**: `damage: 999999` (数值)

### 🧪 测试步骤

#### 准备工作
```bash
# 重启服务器
/ecoenchants reload

# 给自己一剑隔世附魔剑
/ecoenchants give <你的用户名> yijian_geshi 5

# 将附魔书附魔到剑上
# 或者直接给带附魔的剑
/give <你的用户名> diamond_sword{Enchantments:[{id:"ecoenchants:yijian_geshi",lvl:5}]} 1
```

#### 测试场景1：降级效果
```bash
# 设置测试玩家等级为10级（高于附魔等级5）
/xp set <测试玩家> 10 levels

# 用一剑隔世5级剑击杀测试玩家
# 预期结果：测试玩家等级变成5级（10-5=5）

# 检查玩家等级
/xp query <测试玩家> levels
```

#### 测试场景2：秒杀效果
```bash
# 设置测试玩家等级为3级（低于附魔等级5）
/xp set <测试玩家> 3 levels

# 用一剑隔世5级剑攻击测试玩家
# 预期结果：测试玩家直接被秒杀（无视护甲）
```

#### 测试场景3：边界情况
```bash
# 设置测试玩家等级为5级（等于附魔等级5）
/xp set <测试玩家> 5 levels

# 用一剑隔世5级剑攻击测试玩家
# 预期结果：测试玩家直接被秒杀（因为5≤5）
```

### 📊 测试结果记录

#### 预期结果
- ✅ **降级测试**: 高等级玩家被击杀后等级正确降低
- ✅ **秒杀测试**: 低等级玩家被攻击时直接死亡
- ✅ **边界测试**: 等级等于附魔等级的玩家被秒杀
- ✅ **无视防御**: 秒杀效果无视护甲、减伤等

#### 可能的问题
1. **命令权限**: 服务器可能没有权限执行experience命令
2. **条件判断**: %victim_level%变量可能不存在
3. **触发时机**: kill和melee_attack触发器可能有冲突
4. **玩家检测**: %victim% is player条件可能不正确

### 🔧 如果测试失败

#### 问题1：降级不生效
可能原因：experience命令格式不正确
解决方案：尝试其他命令格式，如：
- `"xp add %victim% -%level% levels"`
- `"experience add %victim% -%level%L"`

#### 问题2：秒杀不生效
可能原因：伤害值不够或条件判断错误
解决方案：
- 增加伤害值到更高数值
- 检查条件表达式语法

#### 问题3：条件判断失效
可能原因：变量名不正确
解决方案：查看EcoEnchants文档确认正确的变量名

### 📝 测试命令汇总

```bash
# 基础测试
/ecoenchants give <玩家> yijian_geshi 1    # 1级测试
/ecoenchants give <玩家> yijian_geshi 5    # 5级测试
/ecoenchants give <玩家> yijian_geshi 10   # 10级测试

# 等级设置
/xp set <玩家> 0 levels     # 设置0级
/xp set <玩家> 5 levels     # 设置5级
/xp set <玩家> 10 levels    # 设置10级

# 等级查询
/xp query <玩家> levels     # 查看当前等级
```

---

## 🎯 测试重点

请重点测试以下场景：
1. **高等级玩家被击杀** → 应该降级
2. **低等级玩家被攻击** → 应该直接秒杀
3. **等级刚好等于附魔等级** → 应该秒杀

如果有任何问题，请告诉我具体的测试结果！
