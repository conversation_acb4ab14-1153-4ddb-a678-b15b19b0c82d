display-name: "青蛙跳"
description: "受到不超过256格的摔伤时减少&a%damage_reduce%&r点并弹起"
placeholders:
  damage_reduce: "11 + (%level% - 1)"
type: rare_type

targets:
  - boots
conflicts: []
rarity: rare
max-level: 10

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: fall_damage_reduction
    args:
      reduction: "11 + (%level% - 1)"
      bounce: true
    triggers:
      - take_damage
    conditions:
      - "%damage_cause% = fall"
      - "%fall_distance% <= 256"

conditions: []
