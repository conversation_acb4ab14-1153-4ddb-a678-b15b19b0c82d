#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的YAML语法检查脚本
检查所有附魔文件的YAML语法是否正确
"""

import os
import yaml
import glob

def test_yaml_files():
    """测试所有YAML文件的语法"""
    enchants_dir = "enchants"
    yaml_files = glob.glob(os.path.join(enchants_dir, "*.yml"))
    
    errors = []
    success_count = 0
    
    for yaml_file in yaml_files:
        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                yaml.safe_load(f)
            print(f"✓ {yaml_file}")
            success_count += 1
        except yaml.YAMLError as e:
            error_msg = f"✗ {yaml_file}: {str(e)}"
            print(error_msg)
            errors.append(error_msg)
        except Exception as e:
            error_msg = f"✗ {yaml_file}: {str(e)}"
            print(error_msg)
            errors.append(error_msg)
    
    print(f"\n总计: {len(yaml_files)} 个文件")
    print(f"成功: {success_count} 个文件")
    print(f"错误: {len(errors)} 个文件")
    
    if errors:
        print("\n错误详情:")
        for error in errors:
            print(error)
        return False
    else:
        print("\n所有附魔文件语法检查通过！")
        return True

if __name__ == "__main__":
    test_yaml_files()
