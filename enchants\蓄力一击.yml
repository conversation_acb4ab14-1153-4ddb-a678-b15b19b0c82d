display-name: "蓄力一击"
description: "每击杀&a%kills_needed%&r只生物后，下一次攻击伤害增加&a%damage%%&r"
placeholders:
  kills_needed: "4 - (%level% / 10)"
  damage: "10 + (%level% - 1) * 5"
type: rare_type

targets:
  - sword
  - axe
conflicts: []
rarity: rare
max-level: 20

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: charge_attack
    args:
      kills_required: "4 - (%level% / 10)"
      damage_multiplier: "1.1 + (%level% - 1) * 0.05"
    triggers:
      - kill_entity
      - melee_attack
    conditions: []

conditions: []
