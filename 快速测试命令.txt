# EcoEnchants 快速测试命令
# 用于排查附魔无法获取的问题

## 🔄 基础检查命令
/ecoenchants reload
/ecoenchants version
/ecoenchants list

## 🧪 直接给予附魔书测试
# 史诗类附魔 (应该能正常获取)
/ecoenchants give <玩家> 绑定 1
/ecoenchants give <玩家> 无限耐久 1
/ecoenchants give <玩家> 吸血 3

# 罕见类附魔 (应该能正常获取)
/ecoenchants give <玩家> 吞噬 2
/ecoenchants give <玩家> 横扫千军 3
/ecoenchants give <玩家> 挖掘机 2

# 传说类附魔 (临时启用测试)
/ecoenchants give <玩家> 精进 2
/ecoenchants give <玩家> 万象天引 1

# 限定类附魔 (临时启用测试)
/ecoenchants give <玩家> 万象气息 3
/ecoenchants give <玩家> 混沌威压 2

## 📋 检查附魔信息
/ecoenchants info 绑定
/ecoenchants info 吞噬
/ecoenchants info 精进
/ecoenchants info 万象气息

## 🎮 GUI测试
/ecoenchants gui
/enchant

## 🔧 附魔台测试
/give <玩家> enchanting_table 1
/give <玩家> lapis_lazuli 64
/give <玩家> experience_bottle 32
/give <玩家> diamond_sword 5
/give <玩家> diamond_pickaxe 5
/give <玩家> book 10

## 🐛 问题排查命令

### 检查插件状态
/plugins
/pl EcoEnchants

### 检查权限
/lp user <玩家> permission check ecoenchants.*
/lp user <玩家> permission set ecoenchants.* true

### 检查配置
/ecoenchants debug
/ecoenchants reload

## 📝 测试步骤

1. **重载插件**
   /ecoenchants reload

2. **检查插件版本**
   /ecoenchants version

3. **测试直接给予**
   /ecoenchants give <玩家> 绑定 1

4. **检查背包**
   看是否收到附魔书

5. **测试附魔台**
   用附魔台附魔物品，看是否出现自定义附魔

6. **测试GUI**
   /ecoenchants gui 看是否显示附魔列表

## ⚠️ 常见问题解决

### 如果命令无法执行
- 检查权限: /lp user <玩家> permission set ecoenchants.* true
- 检查插件状态: /plugins

### 如果GUI显示空白
- 确认 discoverable: true
- 确认 enchantable: true
- 重载插件: /ecoenchants reload

### 如果附魔台没有自定义附魔
- 检查 table-chance 是否大于0
- 检查 minimum-level 是否合适
- 确认有足够的经验等级

### 如果附魔效果不生效
- 检查控制台错误信息
- 确认 Paper 版本兼容性
- 检查 effects 配置语法

## 🔍 调试信息收集

如果问题仍然存在，请收集以下信息：

1. **服务器版本**
   /version

2. **插件版本**
   /ecoenchants version

3. **控制台错误**
   查看服务器控制台是否有错误信息

4. **配置文件**
   检查 config.yml, types.yml, rarity.yml 是否正确

5. **附魔文件**
   检查具体附魔的 .yml 文件语法

## 💡 临时解决方案

如果仍然无法获取附魔，可以临时：

1. **提高获取概率**
   在 rarity.yml 中临时提高 table-chance

2. **启用所有获取方式**
   设置 discoverable: true, enchantable: true, tradeable: true

3. **降低最低等级**
   在 rarity.yml 中降低 minimum-level

4. **使用原版附魔ID测试**
   /enchant <玩家> sharpness 1
   确认基础附魔功能正常
