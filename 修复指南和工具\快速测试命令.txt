# EcoEnchants 快速测试命令 - 修复验证

## 🔄 第一步：重启服务器
# 重启服务器以加载新的附魔文件

## 🧪 第二步：基础测试
/ecoenchants reload
/ecoenchants version
/plugins

## 📋 第三步：测试新的英文ID附魔
# 限定类附魔 (红色)
/ecoenchants give <玩家> wanxiang_qixi 5

# 传说类附魔 (金色)
/ecoenchants give <玩家> jingjin 3

# 史诗类附魔 (粉色)
/ecoenchants give <玩家> binding 1

# 罕见类附魔 (淡蓝色)
/ecoenchants give <玩家> devour 2

# 诅咒类附魔 (紫色)
/ecoenchants give <玩家> parasite 2

## 🎮 第四步：GUI测试
/ecoenchants gui

## 📊 第五步：检查附魔信息
/ecoenchants info wanxiang_qixi
/ecoenchants info jingjin
/ecoenchants info binding
/ecoenchants info devour
/ecoenchants info parasite

## ✅ 成功标准
- [ ] 服务器启动无错误信息
- [ ] 所有附魔命令执行成功
- [ ] GUI显示附魔列表
- [ ] 附魔书显示正确的中文名称
- [ ] 颜色显示正确

## 🚨 如果仍有问题
1. 检查服务器控制台错误信息
2. 确认文件名是否符合规范
3. 验证YAML语法正确性
4. 重新运行修复脚本

## 📝 下一步
如果基础测试成功，继续批量重命名剩余的附魔文件：
1. 运行批量重命名脚本
2. 修复所有附魔的内部ID
3. 配置完整的颜色和概率系统
4. 实现所有用户需求功能
