# EcoEnchants 完整修复指南
## 针对Paper 1.18.2的全面修复

### 🔧 已完成的修复

#### 1. 核心配置文件修复
- **config.yml** - 恢复为标准EcoEnchants配置
- **types.yml** - 重新配置6种附魔类型和颜色
- **rarity.yml** - 重新配置获取概率系统

#### 2. 附魔文件修复
创建了基于原版EcoEnchants语法的标准附魔：

##### ✅ 已修复的附魔
1. **心灵遥感** (rare_type) - 掉落物直接进背包
2. **生命偷取** (epic_type) - 攻击时恢复生命值
3. **脉络** (rare_type) - 连锁挖掘矿物
4. **饥饿诅咒** (curse) - 增加饥饿消耗
5. **智慧** (legendary_type) - 经验倍率提升
6. **幸运光环** (limited_type) - 增加幸运度
7. **绑定** (epic_type) - 死亡不掉落
8. **无限耐久** (epic_type) - 防止物品损坏
9. **吸血** (epic_type) - 击杀恢复生命
10. **吞噬** (rare_type) - 击杀恢复饱食度
11. **简单测试** (rare_type) - 基础测试附魔

### 🌈 颜色系统配置

| 类型 | 颜色 | 代码 | 稀有度 | 获取概率 |
|------|------|------|--------|----------|
| normal | 白色 | &f | common | 50% |
| curse | 紫色 | &5 | curse | 10% |
| rare_type | 淡蓝色 | &b | rare | 25% |
| epic_type | 粉色 | &d | epic | 15% |
| legendary_type | 金色 | &6 | legendary | 8% |
| limited_type | 红色 | &c | limited | 3% |

### 🧪 测试命令

#### 基础测试
```bash
# 重载插件
/ecoenchants reload

# 检查插件状态
/ecoenchants version
/plugins

# 测试权限
/lp user <玩家> permission set ecoenchants.* true
```

#### 附魔给予测试
```bash
# 罕见类附魔
/ecoenchants give <玩家> 心灵遥感 1
/ecoenchants give <玩家> 脉络 2
/ecoenchants give <玩家> 吞噬 3

# 史诗类附魔
/ecoenchants give <玩家> 生命偷取 3
/ecoenchants give <玩家> 绑定 1
/ecoenchants give <玩家> 无限耐久 1
/ecoenchants give <玩家> 吸血 5

# 传说类附魔
/ecoenchants give <玩家> 智慧 3

# 限定类附魔
/ecoenchants give <玩家> 幸运光环 5

# 诅咒类附魔
/ecoenchants give <玩家> 饥饿诅咒 2
```

#### GUI测试
```bash
# 打开附魔GUI
/ecoenchants gui

# 检查附魔信息
/ecoenchants info 心灵遥感
/ecoenchants info 生命偷取
/ecoenchants info 智慧
```

#### 附魔台测试
```bash
# 给予测试物品
/give <玩家> enchanting_table 1
/give <玩家> lapis_lazuli 64
/give <玩家> experience_bottle 32
/give <玩家> diamond_sword 5
/give <玩家> diamond_pickaxe 5
/give <玩家> book 10
```

### 🔍 问题排查

#### 如果附魔仍然无法获取：

1. **检查控制台错误**
   - 查看服务器启动时是否有EcoEnchants相关错误
   - 检查附魔文件语法错误

2. **检查权限**
   ```bash
   /lp user <玩家> permission check ecoenchants.*
   /lp user <玩家> permission set ecoenchants.* true
   ```

3. **检查插件版本兼容性**
   ```bash
   /version
   /ecoenchants version
   ```

4. **重新加载配置**
   ```bash
   /ecoenchants reload
   ```

#### 如果GUI显示空白：

1. **检查附魔设置**
   - 确认 `discoverable: true`
   - 确认 `enchantable: true`
   - 确认 `tradeable: true`

2. **检查稀有度配置**
   - 确认 `table-chance` 大于0
   - 确认 `minimum-level` 合理

3. **检查类型配置**
   - 确认types.yml中的类型ID正确
   - 确认附魔文件中的type字段匹配

### 🚀 下一步操作

#### 立即测试：
1. **重启服务器** 或 `/ecoenchants reload`
2. **给自己权限** `/lp user <你的用户名> permission set ecoenchants.* true`
3. **测试基础附魔** `/ecoenchants give <你的用户名> 心灵遥感 1`
4. **检查GUI** `/ecoenchants gui`
5. **测试附魔台** 用附魔台附魔钻石镐

#### 如果基础功能正常：
1. **逐步添加更多附魔** - 基于已修复的模板
2. **调整获取概率** - 根据需要修改rarity.yml
3. **优化附魔效果** - 调整具体的效果参数
4. **添加更多功能** - 实现完整的附魔系统

### 📝 修复原理

#### 为什么之前不工作：
1. **效果ID不兼容** - 使用了EcoEnchants不支持的效果ID
2. **语法错误** - args和triggers的格式不正确
3. **类型配置问题** - 类型ID与附魔文件不匹配
4. **概率设置问题** - 某些稀有度概率为0导致无法获取

#### 修复方法：
1. **使用标准效果ID** - 基于原版EcoEnchants的效果
2. **简化语法** - 移除不必要的args和conditions
3. **统一类型系统** - 确保types.yml和附魔文件匹配
4. **提高测试概率** - 临时提高获取概率便于测试

### ⚠️ 重要提醒

1. **备份数据** - 修改前确保备份重要数据
2. **逐步测试** - 先测试基础功能再添加复杂附魔
3. **监控性能** - 观察服务器性能影响
4. **收集反馈** - 根据实际使用情况调整配置

---

## 🎯 总结

EcoEnchants插件现在应该能够正常工作了。我已经：

1. ✅ **修复了核心配置文件**
2. ✅ **创建了标准的附魔模板**
3. ✅ **实现了颜色系统**
4. ✅ **配置了获取概率**
5. ✅ **提供了完整的测试方案**

**请立即测试基础功能，然后告诉我结果！** 🚀
