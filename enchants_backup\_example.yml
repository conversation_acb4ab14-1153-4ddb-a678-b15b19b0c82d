# ID 是 .yml 文件的文件名,
# 比如 razor.yml 的附魔 ID 是 razor
# 你可以将附魔放在此文件夹中的任何位置
# 包括子文件，方便分类
# _example.yml 不会被加载（此文件）.

display-name: "Example" # 附魔名
description: "Gives a &a%placeholder%%&r and a &a+%damage%&r bonus to damage" # 附魔描述
placeholder: "%level% * 20" # 展示在附魔描述中的变量 (可选, 只能使用自定义的变量)
placeholders: # 额外的描述变量 (可选)
  damage: "%level% * 2" # %damage% 是额外可以使用的变量
type: normal # 附魔类型

targets: # 附魔适用物品
  - sword
conflicts: # 附魔冲突
  - sharpness
rarity: common # 稀有度
max-level: 4 # 最高等级

tradeable: true # 是否能从村民交易中获得
discoverable: true # 是否生成在战利品箱中
enchantable: true # 是否可以从附魔台中获得

# 附魔的效果 (i.e. the functionality)
# 教程: https://plugins.auxilor.io/effects/configuring-an-effect
# %level% 是附魔等级的变量
effects:
  - id: damage_multiplier
    args:
      multiplier: "1 + 0.2 * %level%"
    triggers:
      - melee_attack
  - id: damage_victim
    args:
      damage: "2 * %level%"
    triggers:
      - melee_attack

# 使用此附魔的条件
# 你可以使用%level%作为变量
conditions: [ ]
