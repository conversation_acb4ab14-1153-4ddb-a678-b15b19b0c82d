#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量重命名附魔文件 - 解决EcoEnchants ID规范问题
将中文文件名改为符合 [a-z0-9_] 规范的英文ID
"""

import os
import shutil

# 中文名称到英文ID的映射表
enchant_name_mapping = {
    # 限定类附魔 (5个)
    '万象气息.yml': 'wanxiang_qixi.yml',
    '混沌威压.yml': 'hundun_weiya.yml',
    '一剑隔世.yml': 'yijian_geshi.yml',
    '绝望之力.yml': 'juewang_zhili.yml',
    '睥睨苍生.yml': 'pini_cangsheng.yml',
    
    # 传说类附魔 (5个)
    '精进.yml': 'jingjin.yml',
    '万象天引.yml': 'wanxiang_tianyin.yml',
    '永葆之躯.yml': 'yongbao_zhiqu.yml',
    '圣光领域.yml': 'shengguang_lingyu.yml',
    '普度众生.yml': 'pudu_zhongsheng.yml',
    
    # 史诗类附魔 (30个)
    '绑定.yml': 'binding.yml',
    '无限耐久.yml': 'infinite_durability.yml',
    '吸血.yml': 'lifesteal.yml',
    '再植.yml': 'replant.yml',
    '泰山.yml': 'taishan.yml',
    'AOE.yml': 'aoe.yml',
    '重甲克星.yml': 'armor_piercing.yml',
    '致盲.yml': 'blinding.yml',
    '中毒.yml': 'poison.yml',
    '虚弱.yml': 'weakness.yml',
    '减速.yml': 'slowness.yml',
    '眩晕.yml': 'stunning.yml',
    '凋零.yml': 'wither.yml',
    '钢铁之躯.yml': 'iron_body.yml',
    '新陈代谢.yml': 'metabolism.yml',
    '锻造.yml': 'forging.yml',
    '引力.yml': 'gravity.yml',
    '击飞.yml': 'knockback.yml',
    '退散.yml': 'repel.yml',
    '治愈之光.yml': 'healing_light.yml',
    '利刃.yml': 'sharp_blade.yml',
    '播种机.yml': 'seeder.yml',
    '中国制造.yml': 'made_in_china.yml',
    '破虚.yml': 'void_break.yml',
    '矿石洗练.yml': 'ore_refining.yml',
    '盾构机.yml': 'tunnel_bore.yml',
    '炸弹箭矢.yml': 'explosive_arrow.yml',
    '不服气来干我呀.yml': 'come_fight_me.yml',
    '斗转星移.yml': 'star_shift.yml',
    '重伤.yml': 'heavy_wound.yml',
    
    # 罕见类附魔 (42个)
    '吞噬.yml': 'devour.yml',
    '横扫千军.yml': 'sweep_army.yml',
    '石化皮肤.yml': 'stone_skin.yml',
    '牵引.yml': 'traction.yml',
    '刺骨.yml': 'bone_piercing.yml',
    '伤害加强.yml': 'damage_boost.yml',
    '激素.yml': 'hormone.yml',
    '防御加强.yml': 'defense_boost.yml',
    '扎刺.yml': 'thorns.yml',
    '速度加强.yml': 'speed_boost.yml',
    '鳃.yml': 'gills.yml',
    '夜视.yml': 'night_vision.yml',
    '袋鼠.yml': 'kangaroo.yml',
    '猎豹.yml': 'cheetah.yml',
    '岩浆行走.yml': 'lava_walker.yml',
    '移动修补.yml': 'mobile_repair.yml',
    '矿元素亲和.yml': 'ore_affinity.yml',
    '挖掘机.yml': 'excavator.yml',
    '破甲.yml': 'armor_break.yml',
    '天罚.yml': 'divine_punishment.yml',
    '快速装填.yml': 'quick_reload.yml',
    '海族克星.yml': 'aqua_slayer.yml',
    '忠诚.yml': 'loyalty.yml',
    '耐性.yml': 'endurance.yml',
    '煽动.yml': 'incite.yml',
    '规劝.yml': 'persuade.yml',
    '制动.yml': 'brake.yml',
    '植物亲和.yml': 'plant_affinity.yml',
    '土豆播种机.yml': 'potato_seeder.yml',
    '小麦播种机.yml': 'wheat_seeder.yml',
    '胡萝卜播种机.yml': 'carrot_seeder.yml',
    '甜菜播种机.yml': 'beetroot_seeder.yml',
    '青蛙跳.yml': 'frog_jump.yml',
    '老六.yml': 'old_six.yml',
    '夜伏.yml': 'night_ambush.yml',
    '蓄力一击.yml': 'charged_strike.yml',
    '矿元素亲和2.yml': 'ore_affinity_2.yml',
    '潜力爆发.yml': 'potential_burst.yml',
    '矿脉联锁.yml': 'vein_miner.yml',
    '连跳跳.yml': 'multi_jump.yml',
    '超级能量.yml': 'super_energy.yml',
    '节能科技.yml': 'energy_saving.yml',
    
    # 诅咒类附魔 (10个)
    '寄生虫.yml': 'parasite.yml',
    '重负.yml': 'heavy_burden.yml',
    '易碎.yml': 'fragile.yml',
    '绑定诅咒.yml': 'binding_curse.yml',
    '消失诅咒.yml': 'vanishing_curse.yml',
    '烙印诅咒.yml': 'branding_curse.yml',
    '劣质品诅咒.yml': 'inferior_curse.yml',
    '霉运诅咒.yml': 'bad_luck_curse.yml',
    '空虚诅咒.yml': 'void_curse.yml',
    '经验修补诅咒.yml': 'exp_repair_curse.yml',
    
    # 已有的英文附魔保持不变
    '心灵遥感.yml': 'telekinesis.yml',
    '生命偷取.yml': 'lifesteal_alt.yml',
    '脉络.yml': 'veinminer.yml',
    '智慧.yml': 'wisdom.yml',
    '幸运光环.yml': 'luck_aura.yml',
    '饥饿诅咒.yml': 'hunger_curse.yml',
    '简单测试.yml': 'simple_test.yml'
}

# 英文ID到中文显示名称的映射（用于修复内部ID）
id_to_display_name = {
    # 限定类附魔
    'wanxiang_qixi': '万象气息',
    'hundun_weiya': '混沌威压',
    'yijian_geshi': '一剑隔世',
    'juewang_zhili': '绝望之力',
    'pini_cangsheng': '睥睨苍生',

    # 传说类附魔
    'jingjin': '精进',
    'wanxiang_tianyin': '万象天引',
    'yongbao_zhiqu': '永葆之躯',
    'shengguang_lingyu': '圣光领域',
    'pudu_zhongsheng': '普度众生',

    # 史诗类附魔
    'binding': '绑定',
    'infinite_durability': '无限耐久',
    'lifesteal': '吸血',
    'replant': '再植',
    'taishan': '泰山',
    'aoe': 'AOE',
    'armor_piercing': '重甲克星',
    'blinding': '致盲',
    'poison': '中毒',
    'weakness': '虚弱',
    'slowness': '减速',
    'stunning': '眩晕',
    'wither': '凋零',
    'iron_body': '钢铁之躯',
    'metabolism': '新陈代谢',
    'forging': '锻造',
    'gravity': '引力',
    'knockback': '击飞',
    'repel': '退散',
    'healing_light': '治愈之光',
    'sharp_blade': '利刃',
    'seeder': '播种机',
    'made_in_china': '中国制造',
    'void_break': '破虚',
    'ore_refining': '矿石洗练',
    'tunnel_bore': '盾构机',
    'explosive_arrow': '炸弹箭矢',
    'come_fight_me': '不服气来干我呀',
    'star_shift': '斗转星移',
    'heavy_wound': '重伤'
}

def rename_enchant_files():
    """批量重命名附魔文件"""
    enchants_dir = "../enchants"
    
    if not os.path.exists(enchants_dir):
        print(f"附魔文件夹不存在: {enchants_dir}")
        return
    
    renamed_count = 0
    skipped_count = 0
    error_count = 0
    
    print("开始批量重命名附魔文件...")
    print("=" * 50)
    
    for old_name, new_name in enchant_name_mapping.items():
        old_path = os.path.join(enchants_dir, old_name)
        new_path = os.path.join(enchants_dir, new_name)
        
        if os.path.exists(old_path):
            try:
                # 如果目标文件已存在，先备份
                if os.path.exists(new_path):
                    backup_path = new_path + ".backup"
                    shutil.move(new_path, backup_path)
                    print(f"备份已存在文件: {new_name} -> {new_name}.backup")
                
                # 重命名文件
                shutil.move(old_path, new_path)
                print(f"✅ 重命名: {old_name} -> {new_name}")
                renamed_count += 1
                
            except Exception as e:
                print(f"❌ 重命名失败 {old_name}: {e}")
                error_count += 1
        else:
            print(f"⚠️  文件不存在: {old_name}")
            skipped_count += 1
    
    print("=" * 50)
    print(f"重命名完成！")
    print(f"成功重命名: {renamed_count} 个文件")
    print(f"跳过文件: {skipped_count} 个")
    print(f"错误文件: {error_count} 个")
    
    if renamed_count > 0:
        print("\n✅ 文件重命名完成！现在需要修复文件内部的ID字段。")
        print("请运行 '修复附魔ID.py' 脚本来完成内部ID修复。")
    
    return renamed_count, skipped_count, error_count

if __name__ == "__main__":
    rename_enchant_files()
