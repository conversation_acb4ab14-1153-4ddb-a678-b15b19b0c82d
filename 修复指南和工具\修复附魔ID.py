#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复附魔ID - 修复附魔文件内部的ID字段
确保文件名和内部ID一致，符合EcoEnchants规范
"""

import os
import re

# 英文ID到中文显示名称的映射
id_to_display_name = {
    # 限定类附魔
    'wanxiang_qixi': '万象气息',
    'hundun_weiya': '混沌威压',
    'yijian_geshi': '一剑隔世',
    'juewang_zhili': '绝望之力',
    'pini_cangsheng': '睥睨苍生',
    
    # 传说类附魔
    'jingjin': '精进',
    'wanxiang_tianyin': '万象天引',
    'yongbao_zhiqu': '永葆之躯',
    'shengguang_lingyu': '圣光领域',
    'pudu_zhongsheng': '普度众生',
    
    # 史诗类附魔
    'binding': '绑定',
    'infinite_durability': '无限耐久',
    'lifesteal': '吸血',
    'replant': '再植',
    'taishan': '泰山',
    'aoe': 'AOE',
    'armor_piercing': '重甲克星',
    'blinding': '致盲',
    'poison': '中毒',
    'weakness': '虚弱',
    'slowness': '减速',
    'stunning': '眩晕',
    'wither': '凋零',
    'iron_body': '钢铁之躯',
    'metabolism': '新陈代谢',
    'forging': '锻造',
    'gravity': '引力',
    'knockback': '击飞',
    'repel': '退散',
    'healing_light': '治愈之光',
    'sharp_blade': '利刃',
    'seeder': '播种机',
    'made_in_china': '中国制造',
    'void_break': '破虚',
    'ore_refining': '矿石洗练',
    'tunnel_bore': '盾构机',
    'explosive_arrow': '炸弹箭矢',
    'come_fight_me': '不服气来干我呀',
    'star_shift': '斗转星移',
    'heavy_wound': '重伤',
    
    # 罕见类附魔
    'devour': '吞噬',
    'sweep_army': '横扫千军',
    'stone_skin': '石化皮肤',
    'traction': '牵引',
    'bone_piercing': '刺骨',
    'damage_boost': '伤害加强',
    'hormone': '激素',
    'defense_boost': '防御加强',
    'thorns': '扎刺',
    'speed_boost': '速度加强',
    'gills': '鳃',
    'night_vision': '夜视',
    'kangaroo': '袋鼠',
    'cheetah': '猎豹',
    'lava_walker': '岩浆行走',
    'mobile_repair': '移动修补',
    'ore_affinity': '矿元素亲和',
    'excavator': '挖掘机',
    'armor_break': '破甲',
    'divine_punishment': '天罚',
    'quick_reload': '快速装填',
    'aqua_slayer': '海族克星',
    'loyalty': '忠诚',
    'endurance': '耐性',
    'incite': '煽动',
    'persuade': '规劝',
    'brake': '制动',
    'plant_affinity': '植物亲和',
    'potato_seeder': '土豆播种机',
    'wheat_seeder': '小麦播种机',
    'carrot_seeder': '胡萝卜播种机',
    'beetroot_seeder': '甜菜播种机',
    'frog_jump': '青蛙跳',
    'old_six': '老六',
    'night_ambush': '夜伏',
    'charged_strike': '蓄力一击',
    'ore_affinity_2': '矿元素亲和2',
    'potential_burst': '潜力爆发',
    'vein_miner': '矿脉联锁',
    'multi_jump': '连跳跳',
    'super_energy': '超级能量',
    'energy_saving': '节能科技',
    
    # 诅咒类附魔
    'parasite': '寄生虫',
    'heavy_burden': '重负',
    'fragile': '易碎',
    'binding_curse': '绑定诅咒',
    'vanishing_curse': '消失诅咒',
    'branding_curse': '烙印诅咒',
    'inferior_curse': '劣质品诅咒',
    'bad_luck_curse': '霉运诅咒',
    'void_curse': '空虚诅咒',
    'exp_repair_curse': '经验修补诅咒',
    
    # 已有的英文附魔
    'telekinesis': '心灵遥感',
    'lifesteal_alt': '生命偷取',
    'veinminer': '脉络',
    'wisdom': '智慧',
    'luck_aura': '幸运光环',
    'hunger_curse': '饥饿诅咒',
    'simple_test': '简单测试'
}

def fix_enchant_id(filepath):
    """修复单个附魔文件的ID"""
    try:
        # 从文件名获取ID
        filename = os.path.basename(filepath)
        enchant_id = filename.replace('.yml', '')
        
        # 获取对应的中文显示名称
        display_name = id_to_display_name.get(enchant_id, enchant_id)
        
        # 读取文件内容
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 如果文件为空或只有一行，跳过
        if not content.strip() or len(content.strip().split('\n')) <= 1:
            print(f"⚠️  跳过空文件: {filename}")
            return False
        
        # 修复display-name字段
        content = re.sub(r'^display-name:\s*.*', f'display-name: "{display_name}"', content, flags=re.MULTILINE)
        
        # 如果没有display-name字段，添加一个
        if 'display-name:' not in content:
            lines = content.split('\n')
            lines.insert(0, f'display-name: "{display_name}"')
            content = '\n'.join(lines)
        
        # 写回文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败 {filepath}: {e}")
        return False

def fix_all_enchant_ids():
    """批量修复所有附魔文件的ID"""
    enchants_dir = "../enchants"
    
    if not os.path.exists(enchants_dir):
        print(f"附魔文件夹不存在: {enchants_dir}")
        return
    
    fixed_count = 0
    skipped_count = 0
    total_count = 0
    
    print("开始修复附魔文件内部ID...")
    print("=" * 50)
    
    for filename in os.listdir(enchants_dir):
        if filename.endswith('.yml') and not filename.endswith('.backup'):
            filepath = os.path.join(enchants_dir, filename)
            total_count += 1
            
            enchant_id = filename.replace('.yml', '')
            
            if fix_enchant_id(filepath):
                display_name = id_to_display_name.get(enchant_id, enchant_id)
                print(f"✅ 修复: {filename} -> display-name: {display_name}")
                fixed_count += 1
            else:
                print(f"⚠️  跳过: {filename}")
                skipped_count += 1
    
    print("=" * 50)
    print(f"ID修复完成！")
    print(f"总文件数: {total_count}")
    print(f"成功修复: {fixed_count}")
    print(f"跳过文件: {skipped_count}")
    
    if fixed_count > 0:
        print("\n✅ 附魔ID修复完成！")
        print("现在可以重启服务器测试附魔是否正常加载。")
    
    return fixed_count, skipped_count, total_count

if __name__ == "__main__":
    fix_all_enchant_ids()
