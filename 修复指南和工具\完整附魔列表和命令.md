# EcoEnchants 完整附魔列表和获取命令
## 严格按照用户需求实现的92个附魔系统

### 🔴 限定类附魔 (5个) - 红色，无等级上限

#### 1. 万象气息 (wanxiang_qixi)
- **适用物品**: 锹斧稿
- **最大等级**: 不限
- **效果**: 极大的增加幸运度。当前幸运度+（1-当前幸运度）×0.2，每升1级迭代计算1次
- **获取命令**: `/ecoenchants give <玩家> wanxiang_qixi <等级>`

#### 2. 混沌威压 (hundun_weiya)
- **适用物品**: 剑弓弩斧
- **最大等级**: 不限
- **效果**: 攻击命中时，1%（每升1级，增加1%）的几率给敌人造成25%的最大生命值伤害
- **获取命令**: `/ecoenchants give <玩家> hundun_weiya <等级>`

#### 3. 一剑隔世 (yijian_geshi)
- **适用物品**: 剑
- **最大等级**: 不限
- **效果**: 被该武器击杀的玩家，将降低1级（每升一级加1）等级，等级低于1级（每升一级加1）的，直接秒杀
- **获取命令**: `/ecoenchants give <玩家> yijian_geshi <等级>`

#### 4. 绝望之力 (juewang_zhili)
- **适用物品**: 剑
- **最大等级**: 不限
- **效果**: 每次击杀单位时，有1%几率触发绝望之力。在接下来的100秒内，增加1倍攻击力（每升一级+1倍）。冷却600秒（每升1级减5秒）
- **获取命令**: `/ecoenchants give <玩家> juewang_zhili <等级>`

#### 5. 睥睨苍生 (pini_cangsheng)
- **适用物品**: 剑
- **最大等级**: 不限
- **效果**: 每次攻击单位时，削弱对手1%（每升一级+1）的当前护甲。对手护甲低于1的敌人，直接秒杀
- **获取命令**: `/ecoenchants give <玩家> pini_cangsheng <等级>`

### 🟡 传说类附魔 (5个) - 金色，仅购买获得

#### 1. 精进 (jingjin)
- **适用物品**: 剑弓弩锹斧稿锄鱼竿
- **最大等级**: 附魔书最多1级，可通过宝石升至10级
- **效果**: 手持被附魔的物品吸取的经验量为原本的150%（每升1级加50%）
- **获取命令**: `/ecoenchants give <玩家> jingjin <等级>`

#### 2. 万象天引 (wanxiang_tianyin)
- **适用物品**: 头盔、胸甲、裤子、鞋
- **最大等级**: 附魔书最多1级，可通过宝石升至10级
- **效果**: 使经验和2格（每升1级加1）内的掉落物直接进入背包，效果可叠加
- **获取命令**: `/ecoenchants give <玩家> wanxiang_tianyin <等级>`

#### 3. 永葆之躯 (yongbao_zhiqu)
- **适用物品**: 头盔、胸甲、裤子、鞋
- **最大等级**: 附魔书最多1级，可通过宝石升至10级
- **效果**: 受攻击时，如果血量低于2点，则立即恢复18点血量，冷却215秒（每升1级减少5秒），效果可叠加
- **获取命令**: `/ecoenchants give <玩家> yongbao_zhiqu <等级>`

#### 4. 圣光领域 (shengguang_lingyu)
- **适用物品**: 头盔
- **最大等级**: 附魔书最多1级，可通过宝石升至10级
- **效果**: 自身每秒恢复0.5生命值，同公会的人在附近也可享受同样的治疗效果。有效半径3格（每升1级加2格）
- **获取命令**: `/ecoenchants give <玩家> shengguang_lingyu <等级>`

#### 5. 普度众生 (pudu_zhongsheng)
- **适用物品**: 胸甲
- **最大等级**: 附魔书最多1级，可通过宝石升至10级
- **效果**: 自身获取的经验值增加10%（每升一级额外增加20%）。自身获取经验值时，同公会的人在旁边也会增加同样数值的经验值，半径3格（每升一级加1格）
- **获取命令**: `/ecoenchants give <玩家> pudu_zhongsheng <等级>`

### 🟣 史诗类附魔 (30个) - 粉色，0.1%获取概率

#### 已完成的史诗类附魔 (15个)

1. **绑定** (binding) - 所有物品，1级，死亡不掉落
   - 命令: `/ecoenchants give <玩家> binding 1`

2. **无限耐久** (infinite_durability) - 所有物品，1级，让物品无限耐久
   - 命令: `/ecoenchants give <玩家> infinite_durability 1`

3. **吸血** (lifesteal) - 剑、斧、弓、弩，20级，杀死实体治愈2生命值（每升1级增加0.5）
   - 命令: `/ecoenchants give <玩家> lifesteal <1-20>`

4. **再植** (replant) - 锄头，1级，右击已完全生长的作物，立即收割并重新种植
   - 命令: `/ecoenchants give <玩家> replant 1`

5. **泰山** (taishan) - 头盔、胸甲、裤子、鞋，20级，减少受到的击退效果20%（每升一级增加4%）
   - 命令: `/ecoenchants give <玩家> taishan <1-20>`

6. **斗转星移** (star_shift) - 头盔、胸甲、裤子、鞋，20级，有10%（每升一级增加2%）的机率偏转投掷物和箭矢
   - 命令: `/ecoenchants give <玩家> star_shift <1-20>`

7. **重伤** (heavy_wound) - 剑弓弩斧，20级，击中实体减少治疗效果40%，持续3秒（每升一级增加0.5秒）
   - 命令: `/ecoenchants give <玩家> heavy_wound <1-20>`

8. **AOE** (aoe) - 弓弩，20级，箭矢对实体造成伤害时，对周围敌人造成40%（每升一级增加3%）的伤害，伤害半径为3格（每升10级增加2格）
   - 命令: `/ecoenchants give <玩家> aoe <1-20>`

9. **重甲克星** (armor_piercing) - 剑弓弩斧，20级，对穿戴钻石/合金装备的敌人，每一件造成额外1%（每升一级增加1%）的额外伤害
   - 命令: `/ecoenchants give <玩家> armor_piercing <1-20>`

10. **致盲** (blinding) - 剑弓弩斧，20级，有2%（每升1级增加1%）几率失明敌人
    - 命令: `/ecoenchants give <玩家> blinding <1-20>`

11. **中毒** (poison) - 剑弓弩斧，20级，有2%（每升1级增加1%）几率中毒敌人
    - 命令: `/ecoenchants give <玩家> poison <1-20>`

12. **虚弱** (weakness) - 剑弓弩斧，20级，有2%（每升1级增加1%）几率虚弱敌人
    - 命令: `/ecoenchants give <玩家> weakness <1-20>`

13. **减速** (slowness) - 剑弓弩斧，20级，有5%（每升1级增加1%）几率减速敌人
    - 命令: `/ecoenchants give <玩家> slowness <1-20>`

14. **眩晕** (stunning) - 剑弓弩斧，20级，有2%（每升1级增加1%）几率眩晕敌人
    - 命令: `/ecoenchants give <玩家> stunning <1-20>`

15. **凋零** (wither) - 剑弓弩斧，20级，有5%（每升1级增加1%）几率凋零敌人
    - 命令: `/ecoenchants give <玩家> wither <1-20>`

### 🔵 罕见类附魔 (42个) - 淡蓝色，5%获取概率

#### 已完成的罕见类附魔 (1个)

1. **吞噬** (devour) - 剑、斧、弓、弩，5级，击杀敌人时获得2点（每升1级增加2点）饱食度
   - 命令: `/ecoenchants give <玩家> devour <1-5>`

### 🟣 诅咒类附魔 (10个) - 紫色，负面效果

#### 已完成的诅咒类附魔 (2个)

1. **寄生虫** (parasite) - 所有物品，3级，增加饥饿消耗0.5（每升一级增加0.5）
   - 命令: `/ecoenchants give <玩家> parasite <1-3>`

2. **绑定诅咒** (curse_binding) - 所有物品，1级，被诅咒物品穿在身上后无法卸下
   - 命令: `/ecoenchants give <玩家> curse_binding 1`

---

## 📊 当前进度统计

- **总进度**: 28/92 (30.4%)
- **限定类**: 5/5 (100%) ✅
- **传说类**: 5/5 (100%) ✅
- **史诗类**: 15/30 (50%)
- **罕见类**: 1/42 (2.4%)
- **诅咒类**: 2/10 (20%)

## 🚀 下一步

继续创建剩余的64个附魔，严格按照用户提供的详细需求实现每个附魔的功能。
