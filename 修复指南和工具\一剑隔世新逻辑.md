# 一剑隔世新逻辑设计
## 简化版本，避免复杂变量判断

### 🗡️ 新逻辑说明

由于EcoEnchants的变量系统限制，我们采用简化的逻辑：

#### 方案1：简化效果（当前实现）
- **降级效果**: 击杀玩家时，降低等级数 = 附魔等级
- **伤害加成**: 对玩家造成额外伤害，伤害倍数 = 附魔等级 × 10

**优点**: 
- 可以升级
- 逻辑简单
- 不依赖复杂变量判断

**效果示例**:
- 1级: 击杀降1级，伤害×10
- 5级: 击杀降5级，伤害×50  
- 10级: 击杀降10级，伤害×100

#### 方案2：固定阈值（备选）
为每个常用等级创建固定的效果：

```yaml
# 1级效果
- 击杀降1级
- 对1级以下玩家秒杀

# 5级效果  
- 击杀降5级
- 对5级以下玩家秒杀

# 10级效果
- 击杀降10级
- 对10级以下玩家秒杀
```

### 🔧 当前实现分析

#### 降级效果
```yaml
- id: run_command
  args:
    command: "experience add %victim% -%level% levels"
    as_console: true
  triggers:
    - kill
  conditions:
    - "%victim% is player"
```

**说明**: 击杀玩家时，降低等级数等于附魔等级

#### 伤害加成效果
```yaml
- id: damage_multiplier
  args:
    multiplier: "%level% * 10"
  triggers:
    - melee_attack
  conditions:
    - "%victim% is player"
```

**说明**: 攻击玩家时，伤害倍数 = 附魔等级 × 10

### 🧪 测试方案

#### 测试1：降级效果
```bash
# 给自己5级一剑隔世
/ecoenchants give <你的用户名> yijian_geshi 5

# 设置测试玩家为20级
/xp set <测试玩家> 20 levels

# 击杀测试玩家
# 预期结果：玩家等级变成15级（20-5=15）

# 检查结果
/xp query <测试玩家> levels
```

#### 测试2：伤害加成效果
```bash
# 用5级一剑隔世攻击玩家
# 预期结果：造成50倍伤害（5×10=50）

# 用10级一剑隔世攻击玩家  
# 预期结果：造成100倍伤害（10×10=100）
```

### 🎯 实际效果预期

#### 低等级玩家
- **1级附魔**: 10倍伤害，基本可以秒杀大部分玩家
- **5级附魔**: 50倍伤害，绝对秒杀
- **10级附魔**: 100倍伤害，瞬间秒杀

#### 高等级玩家
- 即使不被秒杀，击杀后也会大幅降级
- 降级效果让玩家损失大量经验

### 🔄 如果效果不理想的替代方案

#### 方案A：纯伤害倍数
```yaml
effects:
  - id: damage_multiplier
    args:
      multiplier: "%level% * 50"  # 更高的倍数
    triggers:
      - melee_attack
    conditions:
      - "%victim% is player"
```

#### 方案B：固定巨额伤害
```yaml
effects:
  - id: damage_victim
    args:
      damage: "999999"  # 固定秒杀伤害
    triggers:
      - melee_attack
    conditions:
      - "%victim% is player"
```

#### 方案C：组合效果
```yaml
effects:
  # 降级效果
  - id: run_command
    args:
      command: "experience add %victim% -%level% levels"
      as_console: true
    triggers:
      - kill
    conditions:
      - "%victim% is player"
  
  # 巨额伤害
  - id: damage_victim
    args:
      damage: 999999
    triggers:
      - melee_attack
    conditions:
      - "%victim% is player"
```

### 📝 测试命令汇总

```bash
# 基础测试
/ecoenchants reload
/ecoenchants give <玩家> yijian_geshi 1
/ecoenchants give <玩家> yijian_geshi 5
/ecoenchants give <玩家> yijian_geshi 10

# 等级设置
/xp set <玩家> 5 levels
/xp set <玩家> 10 levels
/xp set <玩家> 20 levels

# 等级查询
/xp query <玩家> levels

# 给带附魔的剑
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:yijian_geshi",lvl:5}]} 1
```

---

## 🎯 总结

当前的简化逻辑应该能够实现：
1. **可升级**: 支持1-999级
2. **降级效果**: 击杀时降低对应等级
3. **高伤害**: 对玩家造成巨额伤害，实现类似秒杀的效果

请测试这个新逻辑，如果效果不理想，我们可以调整伤害倍数或采用其他方案！
