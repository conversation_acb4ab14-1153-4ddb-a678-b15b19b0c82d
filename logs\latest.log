[00:18:10] [ServerMain/INFO]: Environment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[00:18:11] [Worker-Main-2/INFO]: Loaded 7 recipes
[00:18:13] [Server thread/INFO]: Starting minecraft server version 1.18.2
[00:18:13] [Server thread/INFO]: Loading properties
[00:18:13] [Server thread/INFO]: This server is running Paper version git-Paper-388 (MC: 1.18.2) (Implementing API version 1.18.2-R0.1-SNAPSHOT) (Git: fc9ee65 on ver/1.18.2)
[00:18:13] [Server thread/INFO]: Using 4 threads for Netty based IO
[00:18:13] [Server thread/INFO]: Server Ping Player Sample Count: 12
[00:18:13] [Server thread/INFO]: Default game type: SURVIVAL
[00:18:13] [Server thread/INFO]: Generating keypair
[00:18:13] [Server thread/INFO]: Starting Minecraft server on *:25565
[00:18:13] [Server thread/INFO]: Using default channel type
[00:18:13] [Server thread/INFO]: Paper: Using Java 11 compression from Velocity.
[00:18:13] [Server thread/INFO]: Paper: Using Java cipher from Velocity.
[00:18:15] [Server thread/INFO]: [eco] Initializing eco
[00:18:15] [Server thread/INFO]: [EcoEnchants] Initializing EcoEnchants
[00:18:15] [Server thread/INFO]: [eco] Loading eco v6.76.2
[00:18:15] [Server thread/INFO]: [EcoEnchants] Loading EcoEnchants v12.22.1
[00:18:15] [Server thread/WARN]: [libreforge] Loaded class com.willfp.eco.core.EcoPlugin from eco v6.76.2 which is not a depend or softdepend of this plugin.
[00:18:15] [Server thread/INFO]: [libreforge] Initializing libreforge
[00:18:16] [Server thread/INFO]: Server permissions file permissions.yml is empty, ignoring it
[00:18:16] [Server thread/INFO]: [eco] Enabling eco v6.76.2
[00:18:16] [Server thread/INFO]: [eco] Loading eco
[00:18:17] [Server thread/INFO]: [eco] Scanning for conflicts...
[00:18:17] [Server thread/INFO]: [eco] No conflicts found!
[00:18:17] [Server thread/INFO]: [EcoEnchants] Enabling EcoEnchants v12.22.1
[00:18:17] [Server thread/INFO]: [EcoEnchants] Loading EcoEnchants
[00:18:17] [Server thread/WARN]: **** SERVER IS RUNNING IN OFFLINE/INSECURE MODE!
[00:18:17] [Server thread/WARN]: The server will make no attempt to authenticate usernames. Beware.
[00:18:17] [Server thread/WARN]: While this makes the game possible to play without internet access, it also opens up the ability for hackers to connect with any username they choose.
[00:18:17] [Server thread/WARN]: To change this, set "online-mode" to "true" in the server.properties file.
[00:18:17] [Server thread/INFO]: Preparing level "world"
[00:18:18] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[00:18:18] [Server thread/INFO]: Time elapsed: 257 ms
[00:18:18] [Server thread/INFO]: Preparing start region for dimension minecraft:the_nether
[00:18:18] [Server thread/INFO]: Time elapsed: 71 ms
[00:18:18] [Server thread/INFO]: Preparing start region for dimension minecraft:the_end
[00:18:18] [Server thread/INFO]: Time elapsed: 61 ms
[00:18:18] [Server thread/INFO]: [libreforge] Enabling libreforge v4.75.0
[00:18:18] [Server thread/INFO]: [libreforge] Loading libreforge
[00:18:18] [Server thread/INFO]: [libreforge] 
[00:18:18] [Server thread/INFO]: [libreforge] Hey, what's this plugin doing here? I didn't install it!
[00:18:18] [Server thread/INFO]: [libreforge] libreforge is the effects system for plugins like EcoEnchants,
[00:18:18] [Server thread/INFO]: [libreforge] EcoJobs, EcoItems, etc. If you're looking for config options for
[00:18:18] [Server thread/INFO]: [libreforge] things like cooldown messages, lrcdb, and stuff like that, you'll
[00:18:18] [Server thread/INFO]: [libreforge] find it under /plugins/libreforge
[00:18:18] [Server thread/INFO]: [libreforge] 
[00:18:18] [Server thread/INFO]: [libreforge] Don't worry about updating libreforge, it's handled automatically!
[00:18:18] [Server thread/INFO]: [libreforge] 
[00:18:18] [Server thread/INFO]: Running delayed init tasks
[00:18:19] [Server thread/INFO]: Done (6.099s)! For help, type "help"
[00:18:19] [Server thread/INFO]: Timings Reset
[00:18:19] [Server thread/INFO]: [eco] Loaded eco
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration for damage_nearby_entities found at enchantment aoe -> effects -> args:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'damage_as_player'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) You must specify if the player should be marked as the damager!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration found at enchantment binding -> effects:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'triggers'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) Triggers are not allowed on permanent effects: keep_inventory!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration found at enchantment curse_binding -> effects:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'id'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) Invalid effect ID specified: prevent_item_removal!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration found at enchantment infinite_durability -> effects:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'id'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) Invalid effect ID specified: prevent_item_damage!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration found at enchantment jingjin -> effects:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'triggers'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) Triggers are not allowed on permanent effects: xp_multiplier!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration for potion_effect found at enchantment juewang_zhili -> effects -> args:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'effect'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) You must specify a valid potion effect! See here: https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/potion/PotionEffectType.html
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration for potion_effect found at enchantment juewang_zhili -> effects -> args:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'level'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) You must specify the effect level!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration for potion_effect found at enchantment pini_cangsheng -> effects -> args:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'level'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) You must specify the effect level!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration found at enchantment pudu_zhongsheng -> effects:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'triggers'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) Triggers are not allowed on permanent effects: xp_multiplier!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration for aoe found at enchantment pudu_zhongsheng -> effects -> args:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'shape'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) You must specify a valid shape!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration for aoe found at enchantment shengguang_lingyu -> effects -> args:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'shape'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) You must specify a valid shape!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration for permanent_potion_effect found at enchantment wanxiang_qixi -> effects -> args:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'level'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) You must specify the effect level!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/ERROR]: [EcoEnchants] Invalid configuration found at enchantment wanxiang_tianyin -> effects:
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'triggers'
[00:18:19] [Server thread/ERROR]: [EcoEnchants] (Reason) Triggers are not allowed on permanent effects: telekinesis!
[00:18:19] [Server thread/ERROR]: [EcoEnchants] 
[00:18:19] [Server thread/INFO]: [EcoEnchants] Loaded EcoEnchants
[00:18:19] [Server thread/INFO]: [libreforge] Loaded libreforge
[00:18:21] [Craft Scheduler Thread - 2 - EcoEnchants/WARN]: [EcoEnchants] Failed to check for updates: Connection refused: connect
[00:18:21] [Craft Scheduler Thread - 1 - eco/WARN]: [eco] Failed to check for updates: Connection refused: connect
[00:18:33] [User Authenticator #1/INFO]: UUID of player Nuiing is 348a879b-50ef-3ce9-952e-a94a9954e52a
[00:18:33] [Server thread/INFO]: Nuiing joined the game
[00:18:33] [Server thread/INFO]: Nuiing[/127.0.0.1:51875] logged in with entity id 168 at ([world]49.5, 74.0, -27.5)
[00:18:36] [Server thread/WARN]: UUID of added entity already exists: EntityMinecartChest['Minecart with Chest'/299, uuid='*************-4fd4-a8e3-e8fb13dcec4e', l='ServerLevel[world]', x=73.50, y=-40.50, z=-217.50, cpos=[4, -14], tl=0, v=false]
[00:18:37] [Server thread/WARN]: UUID of added entity already exists: EntityMinecartChest['Minecart with Chest'/319, uuid='c5887dc3-3b30-4907-b859-2df5929ac52d', l='ServerLevel[world]', x=-15.50, y=-24.50, z=-208.50, cpos=[-1, -14], tl=0, v=false]
[00:18:40] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook
[00:18:42] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:18:45] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:18:45] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:18:46] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:18:46] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:18:58] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing legendary
[00:19:05] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing curse
[00:19:32] [Server thread/INFO]: Nuiing issued server command: /gamemode creative
[00:19:32] [Server thread/INFO]: [Nuiing: Set own game mode to Creative Mode]
[00:20:34] [Server thread/INFO]: Nuiing issued server command: /gamemode survival
[00:20:34] [Server thread/INFO]: [Nuiing: Set own game mode to Survival Mode]
[00:20:57] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:20:57] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:20:58] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:20:59] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:20:59] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:20:59] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:21:00] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:21:00] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:21:14] [Server thread/INFO]: Nuiing issued server command: /gamemode creative
[00:21:14] [Server thread/INFO]: [Nuiing: Set own game mode to Creative Mode]
[00:21:49] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:21:49] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:21:50] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:21:50] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:21:51] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:21:51] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:21:51] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:21:52] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:22:27] [Server thread/INFO]: Nuiing issued server command: /gamemode creative
[00:22:32] [Server thread/INFO]: Nuiing issued server command: /gamemode survival
[00:22:32] [Server thread/INFO]: [Nuiing: Set own game mode to Survival Mode]
[00:22:36] [Server thread/INFO]: Nuiing issued server command: /gamemode creative
[00:22:36] [Server thread/INFO]: [Nuiing: Set own game mode to Creative Mode]
[00:22:50] [Server thread/INFO]: Nuiing issued server command: /gamemode survival
[00:22:50] [Server thread/INFO]: [Nuiing: Set own game mode to Survival Mode]
