[00:28:35] [ServerMain/INFO]: Environment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[00:28:37] [Worker-Main-2/INFO]: Loaded 7 recipes
[00:28:39] [Server thread/INFO]: Starting minecraft server version 1.18.2
[00:28:39] [Server thread/INFO]: Loading properties
[00:28:39] [Server thread/INFO]: This server is running Paper version git-Paper-388 (MC: 1.18.2) (Implementing API version 1.18.2-R0.1-SNAPSHOT) (Git: fc9ee65 on ver/1.18.2)
[00:28:39] [Server thread/INFO]: Server Ping Player Sample Count: 12
[00:28:39] [Server thread/INFO]: Using 4 threads for Netty based IO
[00:28:39] [Server thread/INFO]: Default game type: SURVIVAL
[00:28:39] [Server thread/INFO]: Generating keypair
[00:28:39] [Server thread/INFO]: Starting Minecraft server on *:25565
[00:28:39] [Server thread/INFO]: Using default channel type
[00:28:39] [Server thread/INFO]: Paper: Using Java 11 compression from Velocity.
[00:28:39] [Server thread/INFO]: Paper: Using Java cipher from Velocity.
[00:28:40] [Server thread/INFO]: [eco] Initializing eco
[00:28:41] [Server thread/INFO]: [EcoEnchants] Initializing EcoEnchants
[00:28:41] [Server thread/INFO]: [eco] Loading eco v6.76.2
[00:28:41] [Server thread/INFO]: [EcoEnchants] Loading EcoEnchants v12.22.1
[00:28:41] [Server thread/WARN]: [libreforge] Loaded class com.willfp.eco.core.EcoPlugin from eco v6.76.2 which is not a depend or softdepend of this plugin.
[00:28:41] [Server thread/INFO]: [libreforge] Initializing libreforge
[00:28:42] [Server thread/INFO]: Server permissions file permissions.yml is empty, ignoring it
[00:28:42] [Server thread/INFO]: [eco] Enabling eco v6.76.2
[00:28:42] [Server thread/INFO]: [eco] Loading eco
[00:28:42] [Server thread/INFO]: [eco] Scanning for conflicts...
[00:28:42] [Server thread/INFO]: [eco] No conflicts found!
[00:28:42] [Server thread/INFO]: [EcoEnchants] Enabling EcoEnchants v12.22.1
[00:28:42] [Server thread/INFO]: [EcoEnchants] Loading EcoEnchants
[00:28:42] [Server thread/WARN]: **** SERVER IS RUNNING IN OFFLINE/INSECURE MODE!
[00:28:42] [Server thread/WARN]: The server will make no attempt to authenticate usernames. Beware.
[00:28:42] [Server thread/WARN]: While this makes the game possible to play without internet access, it also opens up the ability for hackers to connect with any username they choose.
[00:28:42] [Server thread/WARN]: To change this, set "online-mode" to "true" in the server.properties file.
[00:28:42] [Server thread/INFO]: Preparing level "world"
[00:28:43] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[00:28:44] [Server thread/INFO]: Time elapsed: 274 ms
[00:28:44] [Server thread/INFO]: Preparing start region for dimension minecraft:the_nether
[00:28:44] [Server thread/INFO]: Time elapsed: 76 ms
[00:28:44] [Server thread/INFO]: Preparing start region for dimension minecraft:the_end
[00:28:44] [Server thread/INFO]: Time elapsed: 69 ms
[00:28:44] [Server thread/INFO]: [libreforge] Enabling libreforge v4.75.0
[00:28:44] [Server thread/INFO]: [libreforge] Loading libreforge
[00:28:44] [Server thread/INFO]: [libreforge] 
[00:28:44] [Server thread/INFO]: [libreforge] Hey, what's this plugin doing here? I didn't install it!
[00:28:44] [Server thread/INFO]: [libreforge] libreforge is the effects system for plugins like EcoEnchants,
[00:28:44] [Server thread/INFO]: [libreforge] EcoJobs, EcoItems, etc. If you're looking for config options for
[00:28:44] [Server thread/INFO]: [libreforge] things like cooldown messages, lrcdb, and stuff like that, you'll
[00:28:44] [Server thread/INFO]: [libreforge] find it under /plugins/libreforge
[00:28:44] [Server thread/INFO]: [libreforge] 
[00:28:44] [Server thread/INFO]: [libreforge] Don't worry about updating libreforge, it's handled automatically!
[00:28:44] [Server thread/INFO]: [libreforge] 
[00:28:44] [Server thread/INFO]: Running delayed init tasks
[00:28:45] [Server thread/INFO]: Done (6.240s)! For help, type "help"
[00:28:45] [Server thread/INFO]: Timings Reset
[00:28:45] [Server thread/INFO]: [eco] Loaded eco
[00:28:45] [Server thread/ERROR]: [EcoEnchants] 
[00:28:45] [Server thread/ERROR]: [EcoEnchants] Invalid configuration found at enchantment curse_binding -> effects:
[00:28:45] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'id'
[00:28:45] [Server thread/ERROR]: [EcoEnchants] (Reason) Invalid effect ID specified: curse_of_binding!
[00:28:45] [Server thread/ERROR]: [EcoEnchants] 
[00:28:45] [Server thread/ERROR]: [EcoEnchants] 
[00:28:45] [Server thread/ERROR]: [EcoEnchants] Invalid configuration found at enchantment infinite_durability -> effects:
[00:28:45] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'id'
[00:28:45] [Server thread/ERROR]: [EcoEnchants] (Reason) Invalid effect ID specified: unbreaking!
[00:28:45] [Server thread/ERROR]: [EcoEnchants] 
[00:28:45] [Server thread/ERROR]: [EcoEnchants] 
[00:28:45] [Server thread/ERROR]: [EcoEnchants] Invalid configuration for potion_effect found at enchantment juewang_zhili -> effects -> args:
[00:28:45] [Server thread/ERROR]: [EcoEnchants] (Cause) Argument 'effect'
[00:28:45] [Server thread/ERROR]: [EcoEnchants] (Reason) You must specify a valid potion effect! See here: https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/potion/PotionEffectType.html
[00:28:45] [Server thread/ERROR]: [EcoEnchants] 
[00:28:45] [Server thread/INFO]: [EcoEnchants] Loaded EcoEnchants
[00:28:45] [Server thread/INFO]: [libreforge] Loaded libreforge
[00:28:47] [Craft Scheduler Thread - 3 - EcoEnchants/WARN]: [EcoEnchants] Failed to check for updates: Connection refused: connect
[00:28:47] [Craft Scheduler Thread - 2 - eco/WARN]: [eco] Failed to check for updates: Connection refused: connect
[00:28:52] [User Authenticator #1/INFO]: UUID of player Nuiing is 348a879b-50ef-3ce9-952e-a94a9954e52a
[00:28:52] [Server thread/INFO]: Nuiing joined the game
[00:28:52] [Server thread/INFO]: Nuiing[/127.0.0.1:53787] logged in with entity id 167 at ([world]49.5, 74.0, -27.5)
[00:28:55] [Server thread/WARN]: UUID of added entity already exists: EntityMinecartChest['Minecart with Chest'/299, uuid='*************-4fd4-a8e3-e8fb13dcec4e', l='ServerLevel[world]', x=73.50, y=-40.50, z=-217.50, cpos=[4, -14], tl=0, v=false]
[00:28:55] [Server thread/WARN]: UUID of added entity already exists: EntityMinecartChest['Minecart with Chest'/315, uuid='0cfcb169-8abb-49a8-8553-e3661122faff', l='ServerLevel[world]', x=255.50, y=9.50, z=-39.50, cpos=[15, -3], tl=0, v=false]
[00:28:55] [Server thread/WARN]: UUID of added entity already exists: EntityMinecartChest['Minecart with Chest'/320, uuid='c5887dc3-3b30-4907-b859-2df5929ac52d', l='ServerLevel[world]', x=-15.50, y=-24.50, z=-208.50, cpos=[-1, -14], tl=0, v=false]
[00:29:01] [Server thread/INFO]: Nuiing issued server command: /ecoenchants gui
[00:29:45] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook
[00:29:54] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:29:56] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:29:57] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:29:57] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:29:57] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:29:58] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:29:58] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:29:58] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:29:59] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:29:59] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:29:59] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:00] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook NuiingT
[00:30:00] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook NuiingTT
[00:30:02] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:03] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:04] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:47] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:48] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:49] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:49] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:50] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:50] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:50] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:51] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:52] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:52] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:30:52] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing
[00:31:02] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing legendary
[00:31:07] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing rare
[00:31:08] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing rare
[00:31:09] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing rare
[00:31:09] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing rare
[00:31:10] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing rare
[00:31:10] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing rare
[00:31:11] [Server thread/INFO]: Nuiing issued server command: /ecoenchants giverandombook Nuiing rare
