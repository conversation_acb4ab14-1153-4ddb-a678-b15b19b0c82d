# EcoEnchants 配置文档文件夹

这个文件夹包含了EcoEnchants插件配置过程中生成的所有文档和工具文件。

## 📁 文件说明

### 📋 配置总结文档
- **完整附魔系统配置总结.md** - 完整的系统配置说明，包含颜色系统、获取概率、诅咒附带等所有功能
- **附魔系统总结.md** - 基础的附魔系统总结
- **Paper1.18.2兼容性修复报告.md** - 针对Paper 1.18.2的兼容性修复详情

### 🧪 测试相关文件
- **完整系统测试命令.txt** - 完整的测试命令集合，包含所有功能的测试
- **测试命令.txt** - 基础测试命令
- **测试方案.md** - 详细的测试步骤和方案

### 🔧 工具脚本
- **批量更新附魔类型.py** - 批量更新附魔文件类型和稀有度的Python脚本
- **test_enchants.py** - YAML语法检查脚本

## 🎯 主要功能实现

### 颜色系统
- 普通: 白色 (&f) / 绿色 (&a) 突破等级
- 罕见: 淡蓝色 (&b) - 5%获取概率
- 史诗: 粉色 (&d) - 0.1%获取概率
- 传说: 金色 (&6) - 仅购买获得
- 限定: 红色 (&c) - 仅活动获得
- 诅咒: 紫色 (&5) - 负面效果

### 获取概率
- 罕见类: 5%概率从附魔台、钓鱼、箱子获得
- 史诗类: 0.1%概率获得
- 传说类: 0%概率，仅购买获得
- 限定类: 0%概率，仅特定活动获得

### 诅咒附带
- 普通附魔: 5%概率附带诅咒
- 罕见附魔: 25%概率附带诅咒
- 史诗附魔: 33%概率附带诅咒
- 传说/限定: 不附带诅咒

## 📊 附魔统计

- **限定类**: 5个附魔 (万象气息、混沌威压、一剑隔世、绝望之力、睥睨苍生)
- **传说类**: 5个附魔 (精进、万象天引、永葆之躯、圣光领域、普度众生)
- **史诗类**: 30个附魔 (绑定、无限耐久、吸血等)
- **罕见类**: 42个附魔 (吞噬、横扫千军、挖掘机等)
- **诅咒类**: 10个附魔 (寄生虫、重负、易碎等)

**总计**: 92个自定义附魔

## 🚀 使用说明

1. **部署**: 直接重启服务器即可加载新系统
2. **测试**: 使用测试文档中的命令验证功能
3. **调整**: 根据需要修改概率和效果参数
4. **监控**: 观察服务器性能和玩家反馈

## ⚠️ 注意事项

- 适用于 Paper 1.18.2
- 所有配置已针对该版本优化
- 建议在测试服先验证功能
- 可根据服务器需求调整数值

---

*这些文档文件是配置过程中生成的，主要用于参考和备份。实际的插件配置文件在上级目录中。*
