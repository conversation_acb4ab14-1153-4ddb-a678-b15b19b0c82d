# EcoEnchants 附魔测试命令集合
# 适用于 Paper 1.18.2

## 限定类附魔测试命令
# 万象气息 (锹斧镐)
/ecoenchants give <玩家> 万象气息 1
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:万象气息",lvl:1}]} 1

# 混沌威压 (剑弓弩斧)
/ecoenchants give <玩家> 混沌威压 1
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:混沌威压",lvl:1}]} 1

# 一剑隔世 (剑)
/ecoenchants give <玩家> 一剑隔世 1
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:一剑隔世",lvl:1}]} 1

# 绝望之力 (剑)
/ecoenchants give <玩家> 绝望之力 1
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:绝望之力",lvl:1}]} 1

# 睥睨苍生 (剑)
/ecoenchants give <玩家> 睥睨苍生 1
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:睥睨苍生",lvl:1}]} 1

## 传说类附魔测试命令
# 精进 (多种工具)
/ecoenchants give <玩家> 精进 1
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:精进",lvl:1}]} 1

# 万象天引 (防具)
/ecoenchants give <玩家> 万象天引 1
/give <玩家> diamond_helmet{Enchantments:[{id:"ecoenchants:万象天引",lvl:1}]} 1

# 永葆之躯 (防具)
/ecoenchants give <玩家> 永葆之躯 1
/give <玩家> diamond_chestplate{Enchantments:[{id:"ecoenchants:永葆之躯",lvl:1}]} 1

# 圣光领域 (头盔)
/ecoenchants give <玩家> 圣光领域 1
/give <玩家> diamond_helmet{Enchantments:[{id:"ecoenchants:圣光领域",lvl:1}]} 1

# 普度众生 (胸甲)
/ecoenchants give <玩家> 普度众生 1
/give <玩家> diamond_chestplate{Enchantments:[{id:"ecoenchants:普度众生",lvl:1}]} 1

## 史诗类附魔测试命令
# 绑定 (所有物品)
/ecoenchants give <玩家> 绑定 1
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:绑定",lvl:1}]} 1

# 无限耐久 (所有物品)
/ecoenchants give <玩家> 无限耐久 1
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:无限耐久",lvl:1}]} 1

# 吸血 (剑斧弓弩)
/ecoenchants give <玩家> 吸血 5
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:吸血",lvl:5}]} 1

# 再植 (锄头)
/ecoenchants give <玩家> 再植 1
/give <玩家> diamond_hoe{Enchantments:[{id:"ecoenchants:再植",lvl:1}]} 1

# 泰山 (防具)
/ecoenchants give <玩家> 泰山 5
/give <玩家> diamond_boots{Enchantments:[{id:"ecoenchants:泰山",lvl:5}]} 1

# 锻造 (锹斧镐)
/ecoenchants give <玩家> 锻造 1
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:锻造",lvl:1}]} 1

# 引力 (剑斧)
/ecoenchants give <玩家> 引力 1
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:引力",lvl:1}]} 1

# 击飞 (剑斧)
/ecoenchants give <玩家> 击飞 5
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:击飞",lvl:5}]} 1

# 破虚 (鞋子)
/ecoenchants give <玩家> 破虚 1
/give <玩家> diamond_boots{Enchantments:[{id:"ecoenchants:破虚",lvl:1}]} 1

# 盾构机 (镐)
/ecoenchants give <玩家> 盾构机 2
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:盾构机",lvl:2}]} 1

# 炸弹箭矢 (弓弩)
/ecoenchants give <玩家> 炸弹箭矢 1
/give <玩家> bow{Enchantments:[{id:"ecoenchants:炸弹箭矢",lvl:1}]} 1

## 罕见类附魔测试命令
# 吞噬 (剑斧弓弩)
/ecoenchants give <玩家> 吞噬 3
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:吞噬",lvl:3}]} 1

# 横扫千军 (剑)
/ecoenchants give <玩家> 横扫千军 5
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:横扫千军",lvl:5}]} 1

# 石化皮肤 (防具)
/ecoenchants give <玩家> 石化皮肤 1
/give <玩家> diamond_chestplate{Enchantments:[{id:"ecoenchants:石化皮肤",lvl:1}]} 1

# 挖掘机 (镐)
/ecoenchants give <玩家> 挖掘机 3
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:挖掘机",lvl:3}]} 1

# 矿脉联锁 (镐)
/ecoenchants give <玩家> 矿脉联锁 1
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:矿脉联锁",lvl:1}]} 1

# 连跳跳 (鞋子)
/ecoenchants give <玩家> 连跳跳 5
/give <玩家> diamond_boots{Enchantments:[{id:"ecoenchants:连跳跳",lvl:5}]} 1

## 诅咒类附魔测试命令
# 寄生虫 (所有物品)
/ecoenchants give <玩家> 寄生虫 1
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:寄生虫",lvl:1}]} 1

# 重负 (所有物品)
/ecoenchants give <玩家> 重负 2
/give <玩家> diamond_helmet{Enchantments:[{id:"ecoenchants:重负",lvl:2}]} 1

# 易碎 (所有物品)
/ecoenchants give <玩家> 易碎 1
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:易碎",lvl:1}]} 1

# 绑定诅咒 (所有物品)
/ecoenchants give <玩家> 绑定诅咒 1
/give <玩家> diamond_helmet{Enchantments:[{id:"ecoenchants:绑定诅咒",lvl:1}]} 1

# 消失诅咒 (所有物品)
/ecoenchants give <玩家> 消失诅咒 1
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:消失诅咒",lvl:1}]} 1

# 霉运诅咒 (所有物品)
/ecoenchants give <玩家> 霉运诅咒 1
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:霉运诅咒",lvl:1}]} 1

## 测试场景建议
# 1. 战斗测试：生成一些怪物进行战斗测试
/summon zombie ~ ~ ~ {CustomName:'[{"text":"测试僵尸"}]'}
/summon skeleton ~ ~ ~ {CustomName:'[{"text":"测试骷髅"}]'}

# 2. 挖掘测试：生成一些矿石进行挖掘测试
/fill ~-5 ~-5 ~-5 ~5 ~5 ~5 stone
/fill ~-3 ~-3 ~-3 ~3 ~3 ~3 diamond_ore

# 3. 经验测试：给予经验进行测试
/xp add <玩家> 100

# 4. 重载插件命令
/ecoenchants reload

## 注意事项
# 1. 替换 <玩家> 为实际玩家名
# 2. 某些附魔可能需要特定条件才能触发
# 3. 建议在创造模式下进行测试
# 4. 测试前确保插件已正确加载
# 5. 如果附魔不生效，检查控制台是否有错误信息
