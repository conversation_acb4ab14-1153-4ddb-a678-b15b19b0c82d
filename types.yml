# 附魔类型配置 - 实现完整的颜色系统
# 根据用户需求配置：普通(白/绿)、罕见(淡蓝)、史诗(粉)、传说(金)、限定(红)、诅咒(紫)

types:
  # 普通附魔 - 白色 (原版附魔)
  - id: normal
    format: "&f" # 白色
    limit: -1
    high-level-bias: 0
    no-grindstone: false

  # 普通附魔突破等级 - 绿色 (超过原版上限)
  - id: normal_enhanced
    format: "&a" # 绿色
    limit: -1
    high-level-bias: 0
    no-grindstone: false

  # 罕见附魔 - 淡蓝色 (5%获取概率)
  - id: rare_type
    format: "&b" # 淡蓝色
    limit: -1
    high-level-bias: 0.2
    no-grindstone: false

  # 史诗附魔 - 粉色 (0.1%获取概率)
  - id: epic_type
    format: "&d" # 粉色
    limit: -1
    high-level-bias: 0.3
    no-grindstone: false

  # 传说附魔 - 金色 (只能购买)
  - id: legendary_type
    format: "&6" # 金色
    limit: 3
    high-level-bias: 0
    no-grindstone: true

  # 限定附魔 - 红色 (活动获得，无等级上限)
  - id: limited_type
    format: "&c" # 红色
    limit: 2
    high-level-bias: 0
    no-grindstone: true

  # 诅咒附魔 - 紫色 (负面效果)
  - id: curse_type
    format: "&5" # 紫色
    limit: -1
    high-level-bias: 0
    no-grindstone: false
