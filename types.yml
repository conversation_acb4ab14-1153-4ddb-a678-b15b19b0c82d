# 附魔类型配置 - 基于EcoEnchants标准语法
# 实现颜色系统：普通(白)、罕见(淡蓝)、史诗(粉)、传说(金)、限定(红)、诅咒(紫)

types:
  # 普通附魔 - 白色 (原版附魔)
  - id: normal
    format: "&f"
    limit: -1
    high-level-bias: 0
    no-grindstone: false

  # 诅咒附魔 - 紫色 (负面效果)
  - id: curse
    format: "&5"
    limit: -1
    high-level-bias: 0
    no-grindstone: false

  # 罕见附魔 - 淡蓝色
  - id: rare_type
    format: "&b"
    limit: -1
    high-level-bias: 0.2
    no-grindstone: false

  # 史诗附魔 - 粉色
  - id: epic_type
    format: "&d"
    limit: -1
    high-level-bias: 0.3
    no-grindstone: false

  # 传说附魔 - 金色
  - id: legendary_type
    format: "&6"
    limit: 3
    high-level-bias: 0
    no-grindstone: true

  # 限定附魔 - 红色
  - id: limited_type
    format: "&c"
    limit: 2
    high-level-bias: 0
    no-grindstone: true
