# 附魔类型配置 - 基于正确EcoEnchants语法
# 类型控制附魔的基本属性，颜色通过稀有度控制

types:
  # 普通附魔 - 白色 (原版附魔)
  - id: normal
    format: "&f"
    limit: -1
    high-level-bias: 0
    no-grindstone: false

  # 诅咒附魔 - 紫色 (负面效果)
  - id: curse
    format: "&5"
    limit: -1
    high-level-bias: 0
    no-grindstone: false

  # 特殊附魔 - 用于高级附魔
  - id: special
    format: "<gradient:#FB57EC:#EF1DEC>"
    limit: 1
    high-level-bias: 0.7
    no-grindstone: false
