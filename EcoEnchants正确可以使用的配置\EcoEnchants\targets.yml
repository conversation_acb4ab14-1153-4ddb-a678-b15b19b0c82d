# 这些物品通常无法在附魔台中被附魔,
# 将它们添加到此列表即可在附魔台中被附魔.

extra-enchantable-items:
  - "*elytra"
  - "*shield"
  - "*flint_and_steel"
  - "*shears"
  - "*carrot_on_a_stick"
  - "*player_head"

# 目标由项目和插槽组成。
# 插槽可以是手动、手动、随意、盔甲或任何一种。

# hands: 附魔将在双手中激活
# hand: 附魔只能在主手牌中激活
# offhand: 附魔只有在offhand时才会激活
# armor: 附魔只能在护甲上激活
# any: 只要在你的库存中，附魔就会激活

targets:
  - id: pickaxe
    display-name: "镐"
    slot: hand
    items:
      - "*wooden_pickaxe"
      - "*stone_pickaxe"
      - "*iron_pickaxe"
      - "*golden_pickaxe"
      - "*diamond_pickaxe"
      - "*netherite_pickaxe"

  - id: axe
    display-name: "斧"
    slot: hand
    items:
      - "*wooden_axe"
      - "*stone_axe"
      - "*iron_axe"
      - "*golden_axe"
      - "*diamond_axe"
      - "*netherite_axe"

  - id: hoe
    display-name: "锄"
    slot: hand
    items:
      - "*wooden_hoe"
      - "*stone_hoe"
      - "*iron_hoe"
      - "*golden_hoe"
      - "*diamond_hoe"
      - "*netherite_hoe"

  - id: shovel
    display-name: "锹"
    slot: hand
    items:
      - "*wooden_shovel"
      - "*stone_shovel"
      - "*iron_shovel"
      - "*golden_shovel"
      - "*diamond_shovel"
      - "*netherite_shovel"

  - id: sword
    display-name: "剑"
    slot: hand
    items:
      - "*wooden_sword"
      - "*stone_sword"
      - "*iron_sword"
      - "*golden_sword"
      - "*diamond_sword"
      - "*netherite_sword"

  - id: helmet
    display-name: "头盔"
    slot: armor
    items:
      - "*turtle_helmet"
      - "*leather_helmet"
      - "*chainmail_helmet"
      - "*iron_helmet"
      - "*golden_helmet"
      - "*diamond_helmet"
      - "*netherite_helmet"

  - id: chestplate
    display-name: "胸甲"
    slot: armor
    items:
      - "*leather_chestplate"
      - "*chainmail_chestplate"
      - "*iron_chestplate"
      - "*golden_chestplate"
      - "*diamond_chestplate"
      - "*netherite_chestplate"

  - id: leggings
    display-name: "护腿"
    slot: armor
    items:
      - "*leather_leggings"
      - "*chainmail_leggings"
      - "*iron_leggings"
      - "*golden_leggings"
      - "*diamond_leggings"
      - "*netherite_leggings"

  - id: boots
    display-name: "靴子"
    slot: armor
    items:
      - "*leather_boots"
      - "*chainmail_boots"
      - "*iron_boots"
      - "*golden_boots"
      - "*diamond_boots"
      - "*netherite_boots"

  - id: armor
    display-name: "防具"
    slot: armor
    items:
      - "*leather_boots"
      - "*chainmail_boots"
      - "*iron_boots"
      - "*golden_boots"
      - "*diamond_boots"
      - "*netherite_boots"
      - "*leather_leggings"
      - "*chainmail_leggings"
      - "*iron_leggings"
      - "*golden_leggings"
      - "*diamond_leggings"
      - "*netherite_leggings"
      - "*turtle_helmet"
      - "*leather_helmet"
      - "*chainmail_helmet"
      - "*iron_helmet"
      - "*golden_helmet"
      - "*diamond_helmet"
      - "*netherite_helmet"
      - "*leather_chestplate"
      - "*chainmail_chestplate"
      - "*iron_chestplate"
      - "*golden_chestplate"
      - "*diamond_chestplate"
      - "*netherite_chestplate"

  - id: trident
    display-name: "三叉戟"
    slot: hand
    items:
      - "*trident"

  - id: bow
    display-name: "弓"
    slot: hand
    items:
      - "*bow"

  - id: crossbow
    display-name: "弩"
    slot: hand
    items:
      - "*crossbow"

  - id: shears
    display-name: "剪刀"
    slot: hand
    items:
      - "*shears"

  - id: shield
    display-name: "盾牌"
    slot: hands
    items:
      - "*shield"

  - id: fishing_rod
    display-name: "鱼竿"
    slot: hand
    items:
      - "*fishing_rod"

  - id: flint_and_steel
    display-name: "打火石"
    slot: hand
    items:
      - "*flint_and_steel"

  - id: carrot_on_a_stick
    display-name: "萝卜钓竿"
    slot: hand
    items:
      - "*carrot_on_a_stick"

  - id: elytra
    display-name: "鞘翅"
    slot: armor
    items:
      - "*elytra"
