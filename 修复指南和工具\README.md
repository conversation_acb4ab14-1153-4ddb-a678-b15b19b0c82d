# EcoEnchants 修复指南和工具文件夹

这个文件夹包含了EcoEnchants插件的修复指南、工具脚本和相关文档。

## 📁 文件说明

### 🔧 修复工具
- **批量重命名附魔文件.py** - 将中文文件名改为英文ID
- **修复附魔ID.py** - 批量修复附魔文件中的ID问题
- **验证配置.py** - 检查配置文件语法和兼容性

### 📋 修复指南
- **完整修复方案.md** - 详细的修复步骤和说明
- **错误分析报告.md** - 日志错误分析和解决方案
- **测试验证指南.md** - 完整的测试流程

### 🎯 附魔分类
- **附魔ID映射表.md** - 中文名称到英文ID的映射关系
- **附魔分类配置.md** - 按照用户需求的分类配置

## 🚨 关键问题

根据服务器日志分析，主要问题是：

1. **附魔ID不符合规范** - EcoEnchants要求ID只能包含 `[a-z0-9_]`
2. **中文文件名无法识别** - 需要将文件名改为英文ID
3. **配置文件需要调整** - 确保类型和稀有度配置正确

## 🔧 修复流程

1. 重命名所有附魔文件为英文ID
2. 修复附魔文件内部的ID字段
3. 调整配置文件确保兼容性
4. 测试验证所有功能

---

*此文件夹用于存放修复相关的所有文档和工具，保持主目录整洁。*
