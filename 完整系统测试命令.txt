# EcoEnchants 完整附魔系统测试命令
# 适用于 Paper 1.18.2 - 验证所有功能

## 🔄 基础准备命令
/gamemode creative <玩家>
/ecoenchants reload
/weather clear
/time set day

## 🌈 颜色显示测试

### 限定类附魔测试 (红色 &c)
/ecoenchants give <玩家> 万象气息 5
/ecoenchants give <玩家> 混沌威压 3
/ecoenchants give <玩家> 一剑隔世 2
/ecoenchants give <玩家> 绝望之力 1
/ecoenchants give <玩家> 睥睨苍生 4

### 传说类附魔测试 (金色 &6)
/ecoenchants give <玩家> 精进 5
/ecoenchants give <玩家> 万象天引 3
/ecoenchants give <玩家> 永葆之躯 2
/ecoenchants give <玩家> 圣光领域 4
/ecoenchants give <玩家> 普度众生 1

### 史诗类附魔测试 (粉色 &d)
/ecoenchants give <玩家> 绑定 1
/ecoenchants give <玩家> 无限耐久 1
/ecoenchants give <玩家> 吸血 10
/ecoenchants give <玩家> 泰山 15
/ecoenchants give <玩家> 破虚 5

### 罕见类附魔测试 (淡蓝色 &b)
/ecoenchants give <玩家> 吞噬 3
/ecoenchants give <玩家> 横扫千军 8
/ecoenchants give <玩家> 挖掘机 3
/ecoenchants give <玩家> 矿脉联锁 1
/ecoenchants give <玩家> 连跳跳 10

### 诅咒类附魔测试 (紫色 &5)
/ecoenchants give <玩家> 寄生虫 2
/ecoenchants give <玩家> 重负 3
/ecoenchants give <玩家> 易碎 1
/ecoenchants give <玩家> 绑定诅咒 1
/ecoenchants give <玩家> 霉运诅咒 1

### 等级突破颜色测试 (绿色 &a)
/give <玩家> diamond_sword{Enchantments:[{id:"sharpness",lvl:6}]} 1
/give <玩家> diamond_pickaxe{Enchantments:[{id:"efficiency",lvl:6}]} 1
/give <玩家> bow{Enchantments:[{id:"power",lvl:6}]} 1

## 📊 获取概率测试

### 附魔台概率测试
/give <玩家> enchanting_table 1
/give <玩家> lapis_lazuli 64
/give <玩家> experience_bottle 64
/give <玩家> diamond_sword 10
/give <玩家> diamond_pickaxe 10
/give <玩家> diamond_helmet 10

### 村民交易概率测试
/summon villager ~ ~ ~ {VillagerData:{profession:"librarian",level:5,type:"plains"},Offers:{Recipes:[]}}
/summon villager ~2 ~ ~ {VillagerData:{profession:"librarian",level:5,type:"plains"},Offers:{Recipes:[]}}
/summon villager ~4 ~ ~ {VillagerData:{profession:"librarian",level:5,type:"plains"},Offers:{Recipes:[]}}

### 钓鱼概率测试
/give <玩家> fishing_rod{Enchantments:[{id:"luck_of_the_sea",lvl:3}]} 1
/fill ~-10 ~-1 ~-10 ~10 ~-1 ~10 water

### 战利品箱概率测试
/give <玩家> chest 10
/setblock ~ ~ ~ chest{LootTable:"minecraft:chests/end_city_treasure"}
/setblock ~2 ~ ~ chest{LootTable:"minecraft:chests/stronghold_library"}

## 🔮 诅咒附带测试

### 大量附魔测试 (验证诅咒附带概率)
# 普通附魔 (5%诅咒概率)
/enchant <玩家> sharpness 1
/enchant <玩家> efficiency 1
/enchant <玩家> protection 1

# 使用附魔台大量测试
/give <玩家> book 64
/give <玩家> lapis_lazuli 64

## 🧪 功能效果测试

### 限定类效果测试
# 万象气息 - 幸运度测试
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:万象气息",lvl:5}]} 1
/fill ~-5 ~-5 ~-5 ~5 ~5 ~5 diamond_ore

# 混沌威压 - 最大生命值伤害测试
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:混沌威压",lvl:3}]} 1
/summon zombie ~ ~ ~ {Health:100f,Attributes:[{Name:generic.max_health,Base:100}]}

### 传说类效果测试
# 精进 - 经验倍率测试
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:精进",lvl:3}]} 1
/summon zombie ~ ~ ~

# 万象天引 - 自动拾取测试
/give <玩家> diamond_helmet{Enchantments:[{id:"ecoenchants:万象天引",lvl:2}]} 1
/give <玩家> diamond 10

### 史诗类效果测试
# 绑定 - 死亡不掉落测试
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:绑定",lvl:1}]} 1
/kill <玩家>

# 无限耐久 - 耐久测试
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:无限耐久",lvl:1}],Damage:1000} 1

# 破虚 - 飞行测试
/give <玩家> diamond_boots{Enchantments:[{id:"ecoenchants:破虚",lvl:1}]} 1

### 罕见类效果测试
# 挖掘机 - 范围挖掘测试
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:挖掘机",lvl:3}]} 1
/fill ~-5 ~-5 ~-5 ~5 ~5 ~5 stone

# 矿脉联锁 - 连锁挖掘测试
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:矿脉联锁",lvl:1}]} 1
/fill ~-3 ~-3 ~-3 ~3 ~3 ~3 diamond_ore

# 连跳跳 - 二段跳测试
/give <玩家> diamond_boots{Enchantments:[{id:"ecoenchants:连跳跳",lvl:5}]} 1

### 诅咒类效果测试
# 重负 - 负面效果测试
/give <玩家> diamond_helmet{Enchantments:[{id:"ecoenchants:重负",lvl:2}]} 1

# 易碎 - 耐久损耗测试
/give <玩家> diamond_sword{Enchantments:[{id:"ecoenchants:易碎",lvl:1}]} 1

# 霉运诅咒 - 挖掘失败测试
/give <玩家> diamond_pickaxe{Enchantments:[{id:"ecoenchants:霉运诅咒",lvl:1}]} 1

## 🔍 验证检查命令

### 检查附魔信息
/ecoenchants info 万象气息
/ecoenchants info 精进
/ecoenchants info 绑定
/ecoenchants info 吞噬
/ecoenchants info 寄生虫

### 检查玩家附魔
/ecoenchants list <玩家>

### 检查服务器性能
/tps
/memory

## 📋 测试清单

### 颜色验证 ✓/✗
- [ ] 限定类显示红色
- [ ] 传说类显示金色  
- [ ] 史诗类显示粉色
- [ ] 罕见类显示淡蓝色
- [ ] 诅咒类显示紫色
- [ ] 突破等级显示绿色

### 概率验证 ✓/✗
- [ ] 罕见类5%概率
- [ ] 史诗类0.1%概率
- [ ] 传说类无法常规获取
- [ ] 限定类无法常规获取

### 诅咒附带验证 ✓/✗
- [ ] 普通5%诅咒概率
- [ ] 罕见25%诅咒概率
- [ ] 史诗33%诅咒概率

### 功能验证 ✓/✗
- [ ] 限定类效果正常
- [ ] 传说类效果正常
- [ ] 史诗类效果正常
- [ ] 罕见类效果正常
- [ ] 诅咒类效果正常

## 🚨 问题排查

### 如果颜色不显示
1. 检查客户端是否支持颜色代码
2. 确认types.yml配置正确
3. 重载插件: /ecoenchants reload

### 如果概率不正确
1. 检查rarity.yml配置
2. 确认附魔的rarity字段正确
3. 进行大量测试验证概率

### 如果效果不生效
1. 检查控制台错误信息
2. 确认Paper版本兼容性
3. 验证effects配置语法

### 性能问题
1. 监控TPS: /tps
2. 检查内存使用: /memory
3. 减少同时激活的附魔数量

## 📝 测试记录模板

```
测试日期: ____
测试人员: ____
服务器版本: Paper 1.18.2
插件版本: EcoEnchants ____

颜色测试结果:
- 限定类(红色): ✓/✗
- 传说类(金色): ✓/✗
- 史诗类(粉色): ✓/✗
- 罕见类(淡蓝色): ✓/✗
- 诅咒类(紫色): ✓/✗
- 突破等级(绿色): ✓/✗

概率测试结果:
- 罕见5%: ✓/✗ (实际概率: __%)
- 史诗0.1%: ✓/✗ (实际概率: __%)
- 诅咒附带: ✓/✗

功能测试结果:
- 限定类功能: ✓/✗
- 传说类功能: ✓/✗
- 史诗类功能: ✓/✗
- 罕见类功能: ✓/✗
- 诅咒类功能: ✓/✗

性能测试结果:
- TPS: ____
- 内存使用: ____
- 延迟: ____

问题记录:
1. ________________
2. ________________
3. ________________

总体评价: ✓通过 / ✗需要修复
```
