display-name: "普度众生"
description: "自身获取的经验值增加&a%exp_boost%%&r。自身获取经验值时，同公会的人在&a%radius%&r格内也会增加同样数值的经验值"
placeholders:
  exp_boost: "10 + (%level% - 1) * 20"
  radius: "3 + (%level% - 1)"
type: legendary_type

targets:
  - chestplate
conflicts: []
rarity: legendary
max-level: 10

tradeable: false
discoverable: false
enchantable: false

effects:
  - id: experience_multiplier
    args:
      multiplier: "1.1 + (%level% - 1) * 0.2"
    triggers:
      - gain_experience
    conditions: []
  
  - id: add_permanent_holder_in_radius
    args:
      radius: "3 + (%level% - 1)"
      effects:
        - id: experience_multiplier
          args:
            multiplier: "1.1 + (%level% - 1) * 0.2"
          triggers:
            - gain_experience
      conditions: []

conditions: []
