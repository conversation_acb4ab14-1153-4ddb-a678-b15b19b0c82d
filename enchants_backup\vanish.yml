display-name: "隐匿"
description: "&a%chance%%&r 概率在受到伤害后获得 &a%seconds%&r 秒隐身"
placeholders:
  chance: "2 * %level%"
  seconds: "0.5 + floor(%level% / 4)"
type: normal

targets:
  - armor
conflicts: [ ]
rarity: rare
max-level: 2

tradeable: true
discoverable: true
enchantable: true

effects:
  - id: potion_effect
    args:
      chance: "%level% * 2"
      effect: invisibility
      level: 2
      duration: "10 + floor(%level% * 5)"
      apply-to-player: true
    triggers:
      - take_entity_damage

conditions: [ ]
